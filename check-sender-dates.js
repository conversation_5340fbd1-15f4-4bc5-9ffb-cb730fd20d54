/**
 * 检查特定发件人的邮件日期
 */

async function checkSenderDates() {
  // 清除模块缓存
  delete require.cache[require.resolve('./src/email-service')];
  const emailService = require('./src/email-service');
  
  const email = '<EMAIL>';
  const authCode = 'vzbropcxoztvbhea';
  const sender = 'hxuan25 <<EMAIL>>';
  
  console.log('=== 检查特定发件人的邮件日期 ===');
  console.log(`发件人: ${sender}`);
  
  try {
    // 1. 先搜索该发件人的所有邮件
    console.log('\n1. 搜索该发件人的所有邮件...');
    const allResults = await emailService.searchEmails({
      email: email,
      auth_code: authCode,
      protocol: 'imap',
      sender: sender
    });
    
    console.log(`找到 ${allResults.length} 封邮件`);
    
    if (allResults.length > 0) {
      console.log('\n所有邮件的详细信息:');
      allResults.forEach((mail, index) => {
        console.log(`\n邮件 ${index + 1}:`);
        console.log(`  日期: ${mail.date}`);
        console.log(`  发件人: ${mail.from}`);
        console.log(`  主题: ${mail.subject}`);
        
        // 解析日期
        try {
          const parsedDate = new Date(mail.date);
          console.log(`  解析后日期: ${parsedDate.toISOString().split('T')[0]} (${parsedDate.toDateString()})`);
        } catch (e) {
          console.log(`  日期解析失败: ${e.message}`);
        }
      });
      
      // 2. 基于实际日期进行测试
      console.log('\n2. 基于实际邮件日期进行测试...');
      
      // 获取最早和最晚的日期
      const dates = allResults.map(mail => {
        try {
          return new Date(mail.date);
        } catch (e) {
          return null;
        }
      }).filter(date => date !== null);
      
      if (dates.length > 0) {
        const earliestDate = new Date(Math.min(...dates));
        const latestDate = new Date(Math.max(...dates));
        
        console.log(`\n邮件日期范围:`);
        console.log(`  最早: ${earliestDate.toISOString().split('T')[0]} (${earliestDate.toDateString()})`);
        console.log(`  最晚: ${latestDate.toISOString().split('T')[0]} (${latestDate.toDateString()})`);
        
        // 测试包含实际日期范围的搜索
        const testStartDate = new Date(earliestDate);
        testStartDate.setDate(testStartDate.getDate() - 1); // 前一天
        
        const testEndDate = new Date(latestDate);
        testEndDate.setDate(testEndDate.getDate() + 1); // 后一天
        
        const startDateStr = testStartDate.toISOString().split('T')[0];
        const endDateStr = testEndDate.toISOString().split('T')[0];
        
        console.log(`\n3. 测试包含实际日期的范围搜索 (${startDateStr} 到 ${endDateStr})...`);
        
        const rangeResults = await emailService.searchEmails({
          email: email,
          auth_code: authCode,
          protocol: 'imap',
          sender: sender,
          startDate: startDateStr,
          endDate: endDateStr
        });
        
        console.log(`结果: ${rangeResults.length} 封邮件`);
        
        if (rangeResults.length > 0) {
          console.log('✅ 日期范围搜索成功！');
        } else {
          console.log('❌ 即使包含实际日期范围，搜索仍然失败');
        }
        
        // 4. 测试单独的日期搜索
        console.log(`\n4. 测试单独日期搜索...`);
        for (const mail of allResults.slice(0, 2)) { // 只测试前2封
          try {
            const mailDate = new Date(mail.date);
            const dateStr = mailDate.toISOString().split('T')[0];
            
            console.log(`\n测试日期: ${dateStr}`);
            
            const singleDateResults = await emailService.searchEmails({
              email: email,
              auth_code: authCode,
              protocol: 'imap',
              sender: sender,
              startDate: dateStr,
              endDate: dateStr
            });
            
            console.log(`  结果: ${singleDateResults.length} 封邮件`);
            
          } catch (e) {
            console.log(`  测试失败: ${e.message}`);
          }
        }
      }
    }
    
  } catch (error) {
    console.error('检查失败:', error.message);
  }
}

// 运行检查
if (require.main === module) {
  checkSenderDates()
    .then(() => {
      console.log('\n检查完成');
      process.exit(0);
    })
    .catch(error => {
      console.error('检查过程出错:', error);
      process.exit(1);
    });
}

module.exports = { checkSenderDates };
