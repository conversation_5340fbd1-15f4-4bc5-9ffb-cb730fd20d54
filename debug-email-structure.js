/**
 * 调试邮件结构，查看附件的正确partID
 */

const Imap = require('imap');

async function debugEmailStructure() {
  const email = '<EMAIL>';
  const authCode = 'vzbropcxoztvbhea';
  
  console.log('=== 调试邮件结构 ===');
  
  const imap = new Imap({
    host: 'imap.qq.com',
    port: 993,
    tls: true,
    user: email,
    password: authCode,
    connTimeout: 30000,
    authTimeout: 30000
  });
  
  return new Promise((resolve, reject) => {
    imap.once('ready', () => {
      console.log('IMAP连接就绪');
      
      imap.openBox('INBOX', true, (err, box) => {
        if (err) {
          console.error('打开收件箱失败:', err);
          imap.end();
          reject(err);
          return;
        }
        
        // 查找有附件的邮件
        const searchCriteria = ['ALL'];
        
        imap.search(searchCriteria, (err, results) => {
          if (err) {
            console.error('搜索失败:', err);
            imap.end();
            reject(err);
            return;
          }
          
          // 获取更多邮件进行调试，寻找有附件的邮件
          const recentEmails = results.slice(-100);
          console.log(`检查最近的 ${recentEmails.length} 封邮件...`);
          
          const fetch = imap.fetch(recentEmails, {
            bodies: 'HEADER.FIELDS (FROM TO SUBJECT DATE)',
            struct: true
          });
          
          fetch.on('message', (msg, seqno) => {
            let emailData = {
              uid: null,
              from: '',
              subject: '',
              struct: null
            };
            
            msg.on('body', (stream, info) => {
              let buffer = '';
              stream.on('data', (chunk) => {
                buffer += chunk.toString('utf8');
              });
              stream.once('end', () => {
                const header = Imap.parseHeader(buffer);
                emailData.from = header.from ? header.from[0] : '';
                emailData.subject = header.subject ? header.subject[0] : '';
              });
            });
            
            msg.once('attributes', (attrs) => {
              emailData.uid = attrs.uid;
              emailData.struct = attrs.struct;
              
              console.log(`\n=== 邮件 ${seqno} (UID: ${attrs.uid}) ===`);
              console.log(`主题: ${emailData.subject}`);
              console.log(`发件人: ${emailData.from}`);
              
              if (attrs.struct) {
                console.log('邮件结构:');
                console.log(JSON.stringify(attrs.struct, null, 2));
                
                // 查找附件
                const attachments = findAttachmentsDebug(attrs.struct);
                if (attachments.length > 0) {
                  console.log(`\n找到 ${attachments.length} 个附件:`);
                  attachments.forEach((att, index) => {
                    console.log(`附件 ${index + 1}:`);
                    console.log(`  partID: ${att.partID}`);
                    console.log(`  filename: ${att.filename}`);
                    console.log(`  type: ${att.type}/${att.subtype}`);
                    console.log(`  encoding: ${att.encoding}`);
                    console.log(`  size: ${att.size}`);
                  });
                  
                  // 如果是第一个有附件的邮件，尝试下载
                  if (attachments.length > 0) {
                    console.log('\n尝试下载第一个附件...');
                    testDownloadAttachment(imap, attrs.uid, attachments[0])
                      .then(() => {
                        imap.end();
                        resolve();
                      })
                      .catch((error) => {
                        console.error('下载测试失败:', error);
                        imap.end();
                        reject(error);
                      });
                    return;
                  }
                }
              }
            });
          });
          
          fetch.once('end', () => {
            console.log('没有找到有附件的邮件');
            imap.end();
            resolve();
          });
        });
      });
    });
    
    imap.once('error', (err) => {
      console.error('IMAP连接错误:', err);
      reject(err);
    });
    
    imap.connect();
  });
}

/**
 * 查找附件（调试版本）
 */
function findAttachmentsDebug(struct, attachments = [], prefix = '') {
  for (let i = 0; i < struct.length; i++) {
    const part = struct[i];
    
    if (Array.isArray(part)) {
      // 递归处理嵌套结构
      findAttachmentsDebug(part, attachments, prefix + (i + 1) + '.');
    } else {
      const partID = prefix + (i + 1);
      console.log(`  部分 ${partID}: ${part.type}/${part.subtype}, 编码: ${part.encoding}`);
      
      if (part.disposition) {
        console.log(`    disposition: ${JSON.stringify(part.disposition)}`);
      }
      if (part.params) {
        console.log(`    params: ${JSON.stringify(part.params)}`);
      }
      
      // 检查是否为附件
      if (part.disposition && part.disposition.type === 'attachment') {
        const attachment = {
          partID: partID,
          type: part.type,
          subtype: part.subtype,
          encoding: part.encoding,
          size: part.size,
          filename: part.disposition.params ? part.disposition.params.filename : 'unknown',
          contentType: `${part.type}/${part.subtype}`
        };
        
        // 如果filename在params中
        if (part.params && part.params.name) {
          attachment.filename = part.params.name;
        }
        
        attachments.push(attachment);
      }
    }
  }
  
  return attachments;
}

/**
 * 测试下载附件
 */
async function testDownloadAttachment(imap, uid, attachment) {
  console.log(`测试下载附件: ${attachment.filename}`);
  console.log(`partID: ${attachment.partID}`);
  
  return new Promise((resolve, reject) => {
    // 尝试不同的fetch方式
    console.log('\n方式1: 使用 bodies: partID');
    const fetch1 = imap.fetch(uid, { bodies: attachment.partID });
    
    let buffer1 = '';
    
    fetch1.on('message', (msg, seqno) => {
      msg.on('body', (stream, info) => {
        console.log(`方式1 - info.which: ${info.which}`);
        
        stream.on('data', (chunk) => {
          buffer1 += chunk.toString();
        });
        
        stream.once('end', () => {
          console.log(`方式1 - 接收到数据长度: ${buffer1.length}`);
          console.log(`方式1 - 前100字符: ${buffer1.substring(0, 100)}`);
          
          // 尝试方式2
          console.log('\n方式2: 使用 bodies: [partID]');
          const fetch2 = imap.fetch(uid, { bodies: [attachment.partID] });
          
          let buffer2 = '';
          
          fetch2.on('message', (msg2, seqno2) => {
            msg2.on('body', (stream2, info2) => {
              console.log(`方式2 - info.which: ${info2.which}`);
              
              stream2.on('data', (chunk) => {
                buffer2 += chunk.toString();
              });
              
              stream2.once('end', () => {
                console.log(`方式2 - 接收到数据长度: ${buffer2.length}`);
                console.log(`方式2 - 前100字符: ${buffer2.substring(0, 100)}`);
                resolve();
              });
            });
          });
          
          fetch2.once('error', reject);
        });
      });
    });
    
    fetch1.once('error', reject);
  });
}

// 运行调试
if (require.main === module) {
  debugEmailStructure()
    .then(() => {
      console.log('\n=== 调试完成 ===');
      process.exit(0);
    })
    .catch(error => {
      console.error('调试过程出错:', error);
      process.exit(1);
    });
}

module.exports = { debugEmailStructure };
