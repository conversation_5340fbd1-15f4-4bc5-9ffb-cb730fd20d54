<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端调试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 1200px; margin: 0 auto; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        .download-btn { background: #28a745; padding: 5px 10px; font-size: 12px; }
        .download-btn:hover { background: #218838; }
        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        th, td { padding: 10px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f2f2f2; }
        .log { background: #f8f9fa; padding: 10px; margin: 10px 0; border-radius: 4px; font-family: monospace; font-size: 12px; max-height: 300px; overflow-y: auto; }
        .attachment-info { font-size: 11px; color: #666; }
        .attachment-item { margin-bottom: 2px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>前端调试页面 - 附件功能测试</h1>
        
        <div class="form-group">
            <label for="email">邮箱地址:</label>
            <input type="email" id="email" value="<EMAIL>">
        </div>
        
        <div class="form-group">
            <label for="authCode">授权码:</label>
            <input type="text" id="authCode" value="vzbropcxoztvbhea">
        </div>
        
        <div class="form-group">
            <label for="sender">发件人:</label>
            <input type="text" id="sender" value="<EMAIL>">
        </div>
        
        <div class="form-group">
            <label for="startDate">开始日期:</label>
            <input type="date" id="startDate" value="2023-11-01">
        </div>
        
        <div class="form-group">
            <label for="endDate">结束日期:</label>
            <input type="date" id="endDate" value="2023-11-30">
        </div>
        
        <button onclick="testSearch()">搜索邮件</button>
        <button onclick="clearLog()">清除日志</button>
        
        <div id="log" class="log"></div>
        
        <div id="results" style="display: none;">
            <h3>搜索结果 (<span id="resultCount">0</span>):</h3>
            <table>
                <thead>
                    <tr>
                        <th>UID</th>
                        <th>发件人</th>
                        <th>主题</th>
                        <th>日期</th>
                        <th>附件</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="resultsBody">
                </tbody>
            </table>
        </div>
    </div>

    <script>
        const MCP_SERVER_URL = 'http://localhost:3000';
        
        function log(message) {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${time}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${time}] ${message}`);
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        function formatFileSize(bytes) {
            if (!bytes || bytes === 0) return '0 B';
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(1024));
            return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
        }
        
        function formatDate(dateString) {
            if (!dateString) return '';
            try {
                const date = new Date(dateString);
                return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN');
            } catch (error) {
                return dateString;
            }
        }
        
        async function downloadAttachments(email) {
            log(`🔽 开始下载邮件 ${email.uid} 的 ${email.attachments.length} 个附件...`);
            
            try {
                const downloadDir = `./downloads/debug_test_${email.uid}_${Date.now()}`;
                
                const response = await fetch(`${MCP_SERVER_URL}/mcp/tools`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        tool_name: 'download_attachments',
                        arguments: {
                            email: document.getElementById('email').value,
                            auth_code: document.getElementById('authCode').value,
                            uid: email.uid,
                            attachments: email.attachments,
                            download_dir: downloadDir
                        }
                    })
                });
                
                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.error || response.statusText);
                }
                
                const data = await response.json();
                log(`✅ 下载完成: ${data.results.length} 个文件`);
                
                data.results.forEach((file, index) => {
                    log(`  📄 文件 ${index + 1}: ${file.filename} (${formatFileSize(file.size)})`);
                });
                
                alert(`成功下载 ${data.results.length} 个附件到 ${downloadDir}`);
                
            } catch (error) {
                log(`❌ 下载失败: ${error.message}`);
                alert(`下载失败: ${error.message}`);
            }
        }
        
        async function testSearch() {
            log('🔍 === 开始搜索测试 ===');
            
            const email = document.getElementById('email').value;
            const authCode = document.getElementById('authCode').value;
            const sender = document.getElementById('sender').value;
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            
            log(`📧 邮箱: ${email}`);
            log(`👤 发件人: ${sender}`);
            log(`📅 日期范围: ${startDate} 到 ${endDate}`);
            
            try {
                log('📡 发送搜索请求...');
                
                const searchParams = {
                    email: email,
                    auth_code: authCode,
                    protocol: 'imap',
                    sender: sender || undefined,
                    start_date: startDate || undefined,
                    end_date: endDate || undefined
                };
                
                log('📋 搜索参数: ' + JSON.stringify(searchParams, null, 2));
                
                const response = await fetch(`${MCP_SERVER_URL}/mcp/tools`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        tool_name: 'search_qq_email',
                        arguments: searchParams
                    })
                });
                
                log(`📊 响应状态: ${response.status} ${response.statusText}`);
                
                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.error || response.statusText);
                }
                
                const data = await response.json();
                log(`✅ 搜索成功: ${data.results.length} 封邮件`);
                
                // 统计附件信息
                const emailsWithAttachments = data.results.filter(email => 
                    email.attachments && email.attachments.length > 0
                );
                log(`📎 有附件的邮件: ${emailsWithAttachments.length} 封`);
                
                // 显示详细的附件统计
                if (emailsWithAttachments.length > 0) {
                    log('📎 附件详情:');
                    emailsWithAttachments.forEach((email, index) => {
                        log(`  邮件 ${index + 1} (UID: ${email.uid}): ${email.attachments.length} 个附件`);
                        email.attachments.forEach((att, attIndex) => {
                            log(`    - ${att.filename || '未知文件'} (${formatFileSize(att.size || 0)})`);
                        });
                    });
                }
                
                // 显示结果
                displayResults(data.results);
                
            } catch (error) {
                log(`❌ 搜索失败: ${error.message}`);
                alert('搜索失败: ' + error.message);
            }
        }
        
        function displayResults(results) {
            const resultsDiv = document.getElementById('results');
            const resultsBody = document.getElementById('resultsBody');
            const resultCount = document.getElementById('resultCount');
            
            resultsBody.innerHTML = '';
            resultCount.textContent = results.length;
            
            if (results.length === 0) {
                resultsDiv.style.display = 'none';
                log('📭 没有找到匹配的邮件');
                return;
            }
            
            resultsDiv.style.display = 'block';
            log(`📋 开始显示 ${results.length} 封邮件...`);
            
            results.forEach((email, index) => {
                const hasAttachments = email.attachments && email.attachments.length > 0;
                log(`📧 邮件 ${index + 1}: UID=${email.uid}, 附件=${hasAttachments ? email.attachments.length : 0}个`);
                
                const row = document.createElement('tr');
                
                // UID
                const uidCell = document.createElement('td');
                uidCell.textContent = email.uid || '未知';
                row.appendChild(uidCell);
                
                // 发件人
                const fromCell = document.createElement('td');
                fromCell.textContent = email.from || '未知发件人';
                fromCell.style.maxWidth = '200px';
                fromCell.style.overflow = 'hidden';
                fromCell.style.textOverflow = 'ellipsis';
                fromCell.title = email.from || '未知发件人';
                row.appendChild(fromCell);
                
                // 主题
                const subjectCell = document.createElement('td');
                subjectCell.textContent = email.subject || '无主题';
                subjectCell.style.maxWidth = '300px';
                subjectCell.style.overflow = 'hidden';
                subjectCell.style.textOverflow = 'ellipsis';
                subjectCell.title = email.subject || '无主题';
                row.appendChild(subjectCell);
                
                // 日期
                const dateCell = document.createElement('td');
                dateCell.textContent = formatDate(email.date);
                row.appendChild(dateCell);
                
                // 附件
                const attachmentsCell = document.createElement('td');
                if (hasAttachments) {
                    const attachmentInfo = document.createElement('div');
                    attachmentInfo.className = 'attachment-info';
                    
                    const summary = document.createElement('div');
                    summary.style.fontWeight = 'bold';
                    summary.style.color = '#28a745';
                    summary.textContent = `${email.attachments.length} 个附件`;
                    attachmentInfo.appendChild(summary);
                    
                    email.attachments.forEach((attachment, attIndex) => {
                        const attDiv = document.createElement('div');
                        attDiv.className = 'attachment-item';
                        attDiv.textContent = `${attachment.filename || `附件${attIndex + 1}`} (${formatFileSize(attachment.size || 0)})`;
                        attDiv.title = attachment.filename || `附件${attIndex + 1}`;
                        attachmentInfo.appendChild(attDiv);
                    });
                    
                    attachmentsCell.appendChild(attachmentInfo);
                } else {
                    attachmentsCell.textContent = '无';
                    attachmentsCell.style.color = '#999';
                }
                row.appendChild(attachmentsCell);
                
                // 操作
                const actionsCell = document.createElement('td');
                if (hasAttachments) {
                    const downloadBtn = document.createElement('button');
                    downloadBtn.className = 'download-btn';
                    downloadBtn.textContent = `下载 ${email.attachments.length} 个附件`;
                    downloadBtn.title = `下载邮件 ${email.uid} 的所有附件`;
                    downloadBtn.onclick = () => downloadAttachments(email);
                    actionsCell.appendChild(downloadBtn);
                    
                    log(`✅ 为邮件 ${email.uid} 添加了下载按钮`);
                } else {
                    actionsCell.textContent = '-';
                    actionsCell.style.color = '#999';
                }
                row.appendChild(actionsCell);
                
                resultsBody.appendChild(row);
            });
            
            log(`✅ 结果显示完成: ${results.length} 封邮件`);
            
            // 统计有下载按钮的邮件数量
            const buttonsCount = results.filter(email => 
                email.attachments && email.attachments.length > 0
            ).length;
            log(`🔘 共添加了 ${buttonsCount} 个下载按钮`);
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 调试页面已加载完成');
            log('💡 请填写邮箱信息并点击"搜索邮件"按钮');
        });
    </script>
</body>
</html>
