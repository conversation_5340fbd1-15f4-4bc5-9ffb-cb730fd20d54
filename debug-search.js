/**
 * 调试搜索功能
 */

async function debugSearch() {
  // 清除模块缓存
  delete require.cache[require.resolve('./src/email-service')];
  const emailService = require('./src/email-service');
  
  const email = '<EMAIL>';
  const authCode = 'vzbropcxoztvbhea';
  
  console.log('=== 调试搜索功能 ===');
  
  try {
    // 测试1：无条件搜索（获取最近的邮件）
    console.log('\n1. 测试无条件搜索...');
    const results1 = await emailService.searchEmails({
      email: email,
      auth_code: authCode,
      protocol: 'imap'
    });
    console.log(`无条件搜索结果: ${results1.length} 封邮件`);
    
    if (results1.length > 0) {
      console.log('最近3封邮件的发件人:');
      results1.slice(0, 3).forEach((mail, index) => {
        console.log(`  ${index + 1}. ${mail.from}`);
      });
    }
    
    // 测试2：只按日期搜索
    console.log('\n2. 测试日期范围搜索...');
    const results2 = await emailService.searchEmails({
      email: email,
      auth_code: authCode,
      protocol: 'imap',
      startDate: '2025-07-01',
      endDate: '2025-07-10'
    });
    console.log(`日期范围搜索结果: ${results2.length} 封邮件`);
    
    // 测试3：搜索特定发件人（简化格式）
    console.log('\n3. 测试发件人搜索（简化格式）...');
    const results3 = await emailService.searchEmails({
      email: email,
      auth_code: authCode,
      protocol: 'imap',
      sender: '<EMAIL>'
    });
    console.log(`发件人搜索结果: ${results3.length} 封邮件`);
    
    // 测试4：搜索发件人（部分匹配）
    console.log('\n4. 测试发件人搜索（部分匹配）...');
    const results4 = await emailService.searchEmails({
      email: email,
      auth_code: authCode,
      protocol: 'imap',
      sender: 'hxuan25'
    });
    console.log(`部分匹配搜索结果: ${results4.length} 封邮件`);
    
    // 测试5：搜索发件人（域名匹配）
    console.log('\n5. 测试发件人搜索（域名匹配）...');
    const results5 = await emailService.searchEmails({
      email: email,
      auth_code: authCode,
      protocol: 'imap',
      sender: 'concerthall.com.cn'
    });
    console.log(`域名匹配搜索结果: ${results5.length} 封邮件`);
    
  } catch (error) {
    console.error('调试搜索失败:', error.message);
  }
}

// 运行调试
if (require.main === module) {
  debugSearch()
    .then(() => {
      console.log('\n调试完成');
      process.exit(0);
    })
    .catch(error => {
      console.error('调试过程出错:', error);
      process.exit(1);
    });
}

module.exports = { debugSearch };
