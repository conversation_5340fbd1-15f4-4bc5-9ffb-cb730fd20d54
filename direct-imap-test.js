/**
 * 直接测试IMAP连接，不通过email-service
 */
const Imap = require('imap');

async function directIMAPTest() {
  const email = '<EMAIL>';
  const authCode = 'vzbropcxoztvbhea';
  
  console.log('直接IMAP测试...');
  console.log(`邮箱: ${email}`);
  console.log(`授权码: ${authCode.substring(0, 4)}****`);
  
  // 尝试最简单的配置
  const configs = [
    {
      name: '最简配置',
      config: {
        host: 'imap.qq.com',
        port: 993,
        tls: true,
        user: email,
        password: authCode,
        connTimeout: 30000,
        authTimeout: 30000
      }
    },
    {
      name: '兼容配置',
      config: {
        host: 'imap.qq.com',
        port: 993,
        tls: true,
        tlsOptions: { 
          rejectUnauthorized: false
        },
        user: email,
        password: authCode,
        connTimeout: 30000,
        authTimeout: 30000
      }
    },
    {
      name: '强制TLS1.2',
      config: {
        host: 'imap.qq.com',
        port: 993,
        tls: true,
        tlsOptions: { 
          rejectUnauthorized: false,
          secureProtocol: 'TLSv1_2_method'
        },
        user: email,
        password: authCode,
        connTimeout: 30000,
        authTimeout: 30000
      }
    }
  ];
  
  for (const testConfig of configs) {
    console.log(`\n=== 测试: ${testConfig.name} ===`);
    
    const imap = new Imap(testConfig.config);
    
    try {
      await new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          imap.end();
          reject(new Error('连接超时'));
        }, 30000);
        
        imap.once('ready', () => {
          clearTimeout(timeout);
          console.log('✅ 连接成功！');
          imap.end();
          resolve();
        });
        
        imap.once('error', (err) => {
          clearTimeout(timeout);
          console.log(`❌ 连接失败: ${err.message}`);
          reject(err);
        });
        
        console.log('开始连接...');
        imap.connect();
      });
      
      console.log(`✅ ${testConfig.name} 测试成功！`);
      break; // 找到可用配置，退出循环
      
    } catch (error) {
      console.log(`❌ ${testConfig.name} 测试失败: ${error.message}`);
    }
  }
}

// 运行测试
if (require.main === module) {
  directIMAPTest()
    .then(() => {
      console.log('\n直接IMAP测试完成');
      process.exit(0);
    })
    .catch(error => {
      console.error('测试过程出错:', error);
      process.exit(1);
    });
}

module.exports = { directIMAPTest };
