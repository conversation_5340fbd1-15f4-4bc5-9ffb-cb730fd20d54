/**
 * 最终测试 - 直接调用修复后的email-service
 */

// 强制清除所有相关模块的缓存
function clearModuleCache() {
  const modulesToClear = [
    './src/email-service',
    './src/email-service.js'
  ];
  
  modulesToClear.forEach(modulePath => {
    try {
      const resolvedPath = require.resolve(modulePath);
      delete require.cache[resolvedPath];
      console.log(`清除缓存: ${resolvedPath}`);
    } catch (e) {
      // 模块不存在，忽略
    }
  });
}

async function finalTest() {
  console.log('=== 最终测试开始 ===');
  
  // 清除模块缓存
  clearModuleCache();
  
  // 重新加载模块
  const emailService = require('./src/email-service');
  
  const email = '<EMAIL>';
  const authCode = 'vzbropcxoztvbhea';
  
  console.log(`测试邮箱: ${email}`);
  console.log(`授权码: ${authCode.substring(0, 4)}****`);
  
  try {
    console.log('\n开始调用searchEmails函数...');
    
    const results = await emailService.searchEmails({
      email: email,
      auth_code: authCode,
      protocol: 'imap',
      sender: '',
      subject: '',
      start_date: '',
      end_date: ''
    });
    
    console.log(`\n✅ 搜索成功！找到 ${results.length} 封邮件`);
    
    if (results.length > 0) {
      console.log('\n前3封邮件信息:');
      results.slice(0, 3).forEach((email, index) => {
        console.log(`\n邮件 ${index + 1}:`);
        console.log(`  发件人: ${email.from}`);
        console.log(`  主题: ${email.subject}`);
        console.log(`  日期: ${email.date}`);
      });
    }
    
  } catch (error) {
    console.error('\n❌ 搜索失败:', error.message);
    console.error('错误详情:', error);
  }
}

// 运行测试
if (require.main === module) {
  finalTest()
    .then(() => {
      console.log('\n=== 最终测试完成 ===');
      process.exit(0);
    })
    .catch(error => {
      console.error('测试过程出错:', error);
      process.exit(1);
    });
}

module.exports = { finalTest };
