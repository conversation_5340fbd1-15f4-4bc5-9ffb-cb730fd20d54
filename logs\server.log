

=== 服务器启动于 2025/7/9 23:43:42 ===


=== 服务器退出于 2025/7/9 23:43:42，退出码: null ===


=== 服务器启动于 2025/7/10 00:10:20 ===

[错误] node:events:496
      throw er; // Unhandled 'error' event
      ^

Error: listen EADDRINUSE: address already in use :::3000
    at Server.setupListenHandle [as _listen2] (node:net:1939:16)
    at listenInCluster (node:net:1996:12)
    at Server.listen (node:net:2101:7)
    at Function.listen (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\application.js:635:24)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\qq-email-search-mcp\src\index.js:161:5)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
Emitted 'error' event on Server instance at:
    at emitErrorNT (node:net:1975:8)
    at process.processTicksAndRejections (node:internal/process/task_queues:90:21) {
  code: 'EADDRINUSE',
  errno: -4091,
  syscall: 'listen',
  address: '::',
  port: 3000
}

Node.js v22.16.0

=== 服务器退出于 2025/7/10 00:10:20，退出码: 1 ===


=== 服务器启动于 2025/7/10 00:10:56 ===

QQ邮箱搜索MCP服务器运行在 http://localhost:3000
可用工具: search_qq_email, export_to_excel


=== 服务器启动于 2025/7/10 00:29:47 ===

[错误] node:events:496
      throw er; // Unhandled 'error' event
      ^

Error: listen EADDRINUSE: address already in use :::3000
    at Server.setupListenHandle [as _listen2] (node:net:1939:16)
    at listenInCluster (node:net:1996:12)
    at Server.listen (node:net:2101:7)
    at Function.listen (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\application.js:635:24)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\qq-email-search-mcp\src\index.js:161:5)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
Emitted 'error' event on Server instance at:
    at emitErrorNT (node:net:1975:8)
    at process.processTicksAndRejections (node:internal/process/task_queues:90:21) {
  code: 'EADDRINUSE',
  errno: -4091,
  syscall: 'listen',
  address: '::',
  port: 3000
}

Node.js v22.16.0

=== 服务器退出于 2025/7/10 00:29:48，退出码: 1 ===


=== 服务器启动于 2025/7/10 00:30:34 ===

QQ邮箱搜索MCP服务器运行在 http://localhost:3000
可用工具: search_qq_email, export_to_excel
关闭MCP服务器...

=== 服务器退出于 2025/7/10 00:33:26，退出码: null ===


=== 服务器启动于 2025/7/10 00:34:22 ===


=== 服务器退出于 2025/7/10 00:34:22，退出码: null ===


=== 服务器启动于 2025/7/10 00:35:28 ===

QQ邮箱搜索MCP服务器运行在 http://localhost:3000
可用工具: search_qq_email, export_to_excel


=== 服务器启动于 2025/7/10 00:54:39 ===

QQ邮箱搜索MCP服务器运行在 http://localhost:3000
可用工具: search_qq_email, export_to_excel


=== 服务器启动于 2025/7/10 01:01:16 ===

QQ邮箱搜索MCP服务器运行在 http://localhost:3000
可用工具: search_qq_email, export_to_excel


=== 服务器启动于 2025/7/10 01:05:35 ===

[错误] node:events:496
      throw er; // Unhandled 'error' event
      ^

Error: listen EADDRINUSE: address already in use :::3000
    at Server.setupListenHandle [as _listen2] (node:net:1939:16)
    at listenInCluster (node:net:1996:12)
    at Server.listen (node:net:2101:7)
    at Function.listen (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\application.js:635:24)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\qq-email-search-mcp\src\index.js:166:5)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
Emitted 'error' event on Server instance at:
    at emitErrorNT (node:net:1975:8)
    at process.processTicksAndRejections (node:internal/process/task_queues:90:21) {
  code: 'EADDRINUSE',
  errno: -4091,
  syscall: 'listen',
  address: '::',
  port: 3000
}

Node.js v22.16.0

=== 服务器退出于 2025/7/10 01:05:36，退出码: 1 ===


=== 服务器启动于 2025/7/10 01:05:54 ===

[错误] node:events:496
      throw er; // Unhandled 'error' event
      ^

Error: listen EADDRINUSE: address already in use :::3000
    at Server.setupListenHandle [as _listen2] (node:net:1939:16)
    at listenInCluster (node:net:1996:12)
    at Server.listen (node:net:2101:7)
    at Function.listen (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\application.js:635:24)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\qq-email-search-mcp\src\index.js:166:5)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
Emitted 'error' event on Server instance at:
    at emitErrorNT (node:net:1975:8)
    at process.processTicksAndRejections (node:internal/process/task_queues:90:21) {
  code: 'EADDRINUSE',
  errno: -4091,
  syscall: 'listen',
  address: '::',
  port: 3000
}

Node.js v22.16.0

=== 服务器退出于 2025/7/10 01:05:55，退出码: 1 ===


=== 服务器启动于 2025/7/10 01:06:20 ===

QQ邮箱搜索MCP服务器运行在 http://localhost:3000
可用工具: search_qq_email, export_to_excel
收到MCP工具请求: search_qq_email
[错误] 搜索邮件错误: Error: No supported authentication method(s) available. Unable to login.
    at Connection.<anonymous> (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Connection.js:1679:15)
    at Connection._resTagged (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Connection.js:1535:22)
    at Parser.<anonymous> (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Connection.js:194:10)
    at Parser.emit (node:events:518:28)
    at Parser._resTagged (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Parser.js:175:10)
    at Parser._parse (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Parser.js:139:16)
    at Parser._tryread (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Parser.js:82:15)
    at Parser._cbReadable (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Parser.js:53:12)
    at TLSSocket.emit (node:events:518:28)
    at emitReadable_ (node:internal/streams/readable:834:12) {
  source: 'authentication'
}
收到MCP工具请求: search_qq_email
[错误] 搜索邮件错误: Error: No supported authentication method(s) available. Unable to login.
    at Connection.<anonymous> (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Connection.js:1679:15)
    at Connection._resTagged (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Connection.js:1535:22)
    at Parser.<anonymous> (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Connection.js:194:10)
    at Parser.emit (node:events:518:28)
    at Parser._resTagged (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Parser.js:175:10)
    at Parser._parse (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Parser.js:139:16)
    at Parser._tryread (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Parser.js:82:15)
    at Parser._cbReadable (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Parser.js:53:12)
    at TLSSocket.emit (node:events:518:28)
    at emitReadable_ (node:internal/streams/readable:834:12) {
  source: 'authentication'
}


=== 服务器启动于 2025/7/10 08:37:14 ===

QQ邮箱搜索MCP服务器运行在 http://localhost:3000
可用工具: search_qq_email, export_to_excel
收到MCP工具请求: search_qq_email
[错误] 搜索邮件错误: Error: No supported authentication method(s) available. Unable to login.
    at Connection.<anonymous> (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Connection.js:1679:15)
    at Connection._resTagged (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Connection.js:1535:22)
    at Parser.<anonymous> (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Connection.js:194:10)
    at Parser.emit (node:events:518:28)
    at Parser._resTagged (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Parser.js:175:10)
    at Parser._parse (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Parser.js:139:16)
    at Parser._tryread (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Parser.js:82:15)
    at Parser._cbReadable (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Parser.js:53:12)
    at TLSSocket.emit (node:events:518:28)
    at emitReadable_ (node:internal/streams/readable:834:12) {
  source: 'authentication'
}


=== 服务器启动于 2025/7/10 08:45:57 ===

[错误] node:events:496
      throw er; // Unhandled 'error' event
      ^

Error: listen EADDRINUSE: address already in use :::3000
    at Server.setupListenHandle [as _listen2] (node:net:1939:16)
    at listenInCluster (node:net:1996:12)
    at Server.listen (node:net:2101:7)
    at Function.listen (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\application.js:635:24)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\qq-email-search-mcp\src\index.js:196:5)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
Emitted 'error' event on Server instance at:
    at emitErrorNT (node:net:1975:8)
    at process.processTicksAndRejections (node:internal/process/task_queues:90:21) {
  code: 'EADDRINUSE',
  errno: -4091,
  syscall: 'listen',
  address: '::',
  port: 3000
}

Node.js v22.16.0

=== 服务器退出于 2025/7/10 08:45:58，退出码: 1 ===

=== 服务器退出于 2025/7/10 08:46:25，退出码: 1 ===


=== 服务器启动于 2025/7/10 08:46:38 ===

QQ邮箱搜索MCP服务器运行在 http://localhost:3000
可用工具: search_qq_email, export_to_excel
收到MCP工具请求: search_qq_email
[错误] 搜索邮件错误: Error: No supported authentication method(s) available. Unable to login.
    at Connection.<anonymous> (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Connection.js:1679:15)
    at Connection._resTagged (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Connection.js:1535:22)
    at Parser.<anonymous> (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Connection.js:194:10)
    at Parser.emit (node:events:518:28)
    at Parser._resTagged (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Parser.js:175:10)
    at Parser._parse (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Parser.js:139:16)
    at Parser._tryread (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Parser.js:82:15)
    at Parser._cbReadable (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Parser.js:53:12)
    at TLSSocket.emit (node:events:518:28)
    at emitReadable_ (node:internal/streams/readable:834:12) {
  source: 'authentication'
}
开始测试QQ邮箱连接...
邮箱: <EMAIL>
授权码: vzbr****

=== 测试配置: 标准配置 ===
[connection] Connected to host
<= '* OK [CAPABILITY IMAP4 IMAP4rev1 ID AUTH=PLAIN AUTH=LOGIN AUTH=XOAUTH2 NAMESPACE] QQMail XMIMAP4Server ready'
=> 'A0 CAPABILITY'
<= '* CAPABILITY IMAP4 IMAP4rev1 XLIST MOVE IDLE XAPPLEPUSHSERVICE SASL-IR AUTH=PLAIN AUTH=LOGIN AUTH=XOAUTH2 NAMESPACE CHILDREN ID UIDPLUS'
<= 'A0 OK CAPABILITY Completed'
=> 'A1 LOGIN "<EMAIL>" "vzbropcxoztvbhea"'
<= 'A1 OK Success login ok'
=> 'A2 CAPABILITY'
<= '* CAPABILITY IMAP4 IMAP4rev1 XLIST MOVE IDLE XAPPLEPUSHSERVICE NAMESPACE CHILDREN ID UIDPLUS COMPRESS=DEFLATE'
<= 'A2 OK CAPABILITY Completed'
=> 'A3 NAMESPACE'
<= '* NAMESPACE (("" "/")) NIL NIL'
<= 'A3 OK NAMESPACE Success'
=> 'A4 LIST "" ""'
<= '* LIST (\\NoSelect) "/" "/"'
<= 'A4 OK LIST completed'
✓ 连接成功！
=> 'A5 LOGOUT'

🎉 找到可用配置: 标准配置
<= '* BYE LOGOUT received'
<= 'A5 OK LOGOUT Completed'
[connection] Ended
[connection] Closed
收到MCP工具请求: search_qq_email
[错误] 搜索邮件错误: Error: No supported authentication method(s) available. Unable to login.
    at Connection.<anonymous> (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Connection.js:1679:15)
    at Connection._resTagged (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Connection.js:1535:22)
    at Parser.<anonymous> (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Connection.js:194:10)
    at Parser.emit (node:events:518:28)
    at Parser._resTagged (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Parser.js:175:10)
    at Parser._parse (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Parser.js:139:16)
    at Parser._tryread (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Parser.js:82:15)
    at Parser._cbReadable (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Parser.js:53:12)
    at TLSSocket.emit (node:events:518:28)
    at emitReadable_ (node:internal/streams/readable:834:12) {
  source: 'authentication'
}


=== 服务器启动于 2025/7/10 08:52:40 ===

QQ邮箱搜索MCP服务器运行在 http://localhost:3000
可用工具: search_qq_email, export_to_excel


=== 服务器启动于 2025/7/10 09:07:48 ===

QQ邮箱搜索MCP服务器运行在 http://localhost:3000
可用工具: search_qq_email, export_to_excel
开始nodemailer测试...
[错误] 连接测试错误: TypeError: nodemailer.createTransporter is not a function
    at testQQEmailWithNodemailer (C:\Users\<USER>\Desktop\qq-email-search-mcp\test-nodemailer.js:13:34)
    at C:\Users\<USER>\Desktop\qq-email-search-mcp\src\index.js:42:37
    at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\router\layer.js:95:5)
    at next (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\router\route.js:149:13)
    at Route.dispatch (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\router\route.js:119:3)
    at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\router\layer.js:95:5)
    at C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\router\index.js:284:15
    at Function.process_params (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\router\index.js:346:12)
    at next (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\router\index.js:280:10)
    at serveStatic (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\serve-static\index.js:75:16)
使用nodemailer测试QQ邮箱连接...
邮箱: <EMAIL>
授权码: vzbr****


=== 服务器启动于 2025/7/10 09:09:54 ===

QQ邮箱搜索MCP服务器运行在 http://localhost:3000
可用工具: search_qq_email, export_to_excel
开始nodemailer测试...
使用nodemailer测试QQ邮箱连接...
邮箱: <EMAIL>
授权码: vzbr****
[错误] 连接测试错误: TypeError: nodemailer.createTransporter is not a function
    at testQQEmailWithNodemailer (C:\Users\<USER>\Desktop\qq-email-search-mcp\test-nodemailer.js:13:34)
    at C:\Users\<USER>\Desktop\qq-email-search-mcp\src\index.js:42:37
    at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\router\layer.js:95:5)
    at next (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\router\route.js:149:13)
    at Route.dispatch (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\router\route.js:119:3)
    at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\router\layer.js:95:5)
    at C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\router\index.js:284:15
    at Function.process_params (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\router\index.js:346:12)
    at next (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\router\index.js:280:10)
    at serveStatic (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\serve-static\index.js:75:16)
收到MCP工具请求: search_qq_email
=== searchEmails 函数开始 ===
接收到的参数: {
  "email": "<EMAIL>",
  "auth_code": "vzbropcxoztvbhea",
  "protocol": "imap",
  "sender": "hxuan25 <<EMAIL>>",
  "startDate": "2025-06-10",
  "endDate": "2025-07-10"
}
准备搜索邮件，邮箱: <EMAIL>, 协议: imap, 发件人: hxuan25 <<EMAIL>>, 主题: 未指定, 日期范围: 2025-06-10 至 2025-07-10
使用IMAP协议搜索...
即将调用 searchEmailsWithIMAP...
=== 开始IMAP搜索 ===
邮箱: <EMAIL>
发件人过滤: hxuan25 <<EMAIL>>
主题过滤: 无
开始连接IMAP...
IMAP连接就绪，开始搜索...
收件箱打开成功，共有 7182 封邮件
搜索条件: [
  'ALL',
  [ 'SINCE', 2025-06-10T00:00:00.000Z ],
  [ 'BEFORE', 2025-07-10T00:00:00.000Z ],
  [ 'FROM', 'hxuan25 <<EMAIL>>' ]
]
搜索到 0 封邮件
searchEmailsWithIMAP 调用完成
IMAP连接已关闭
开始nodemailer测试...
使用nodemailer测试QQ邮箱连接...
邮箱: <EMAIL>
授权码: vzbr****
[错误] 连接测试错误: TypeError: nodemailer.createTransporter is not a function
    at testQQEmailWithNodemailer (C:\Users\<USER>\Desktop\qq-email-search-mcp\test-nodemailer.js:13:34)
    at C:\Users\<USER>\Desktop\qq-email-search-mcp\src\index.js:42:37
    at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\router\layer.js:95:5)
    at next (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\router\route.js:149:13)
    at Route.dispatch (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\router\route.js:119:3)
    at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\router\layer.js:95:5)
    at C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\router\index.js:284:15
    at Function.process_params (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\router\index.js:346:12)
    at next (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\router\index.js:280:10)
    at serveStatic (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\serve-static\index.js:75:16)
