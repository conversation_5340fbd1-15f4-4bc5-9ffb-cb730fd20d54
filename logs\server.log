

=== 服务器启动于 2025/7/9 23:43:42 ===


=== 服务器退出于 2025/7/9 23:43:42，退出码: null ===


=== 服务器启动于 2025/7/10 00:10:20 ===

[错误] node:events:496
      throw er; // Unhandled 'error' event
      ^

Error: listen EADDRINUSE: address already in use :::3000
    at Server.setupListenHandle [as _listen2] (node:net:1939:16)
    at listenInCluster (node:net:1996:12)
    at Server.listen (node:net:2101:7)
    at Function.listen (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\application.js:635:24)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\qq-email-search-mcp\src\index.js:161:5)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
Emitted 'error' event on Server instance at:
    at emitErrorNT (node:net:1975:8)
    at process.processTicksAndRejections (node:internal/process/task_queues:90:21) {
  code: 'EADDRINUSE',
  errno: -4091,
  syscall: 'listen',
  address: '::',
  port: 3000
}

Node.js v22.16.0

=== 服务器退出于 2025/7/10 00:10:20，退出码: 1 ===


=== 服务器启动于 2025/7/10 00:10:56 ===

QQ邮箱搜索MCP服务器运行在 http://localhost:3000
可用工具: search_qq_email, export_to_excel


=== 服务器启动于 2025/7/10 00:29:47 ===

[错误] node:events:496
      throw er; // Unhandled 'error' event
      ^

Error: listen EADDRINUSE: address already in use :::3000
    at Server.setupListenHandle [as _listen2] (node:net:1939:16)
    at listenInCluster (node:net:1996:12)
    at Server.listen (node:net:2101:7)
    at Function.listen (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\application.js:635:24)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\qq-email-search-mcp\src\index.js:161:5)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
Emitted 'error' event on Server instance at:
    at emitErrorNT (node:net:1975:8)
    at process.processTicksAndRejections (node:internal/process/task_queues:90:21) {
  code: 'EADDRINUSE',
  errno: -4091,
  syscall: 'listen',
  address: '::',
  port: 3000
}

Node.js v22.16.0

=== 服务器退出于 2025/7/10 00:29:48，退出码: 1 ===


=== 服务器启动于 2025/7/10 00:30:34 ===

QQ邮箱搜索MCP服务器运行在 http://localhost:3000
可用工具: search_qq_email, export_to_excel
关闭MCP服务器...

=== 服务器退出于 2025/7/10 00:33:26，退出码: null ===


=== 服务器启动于 2025/7/10 00:34:22 ===


=== 服务器退出于 2025/7/10 00:34:22，退出码: null ===


=== 服务器启动于 2025/7/10 00:35:28 ===

QQ邮箱搜索MCP服务器运行在 http://localhost:3000
可用工具: search_qq_email, export_to_excel


=== 服务器启动于 2025/7/10 00:54:39 ===

QQ邮箱搜索MCP服务器运行在 http://localhost:3000
可用工具: search_qq_email, export_to_excel


=== 服务器启动于 2025/7/10 01:01:16 ===

QQ邮箱搜索MCP服务器运行在 http://localhost:3000
可用工具: search_qq_email, export_to_excel


=== 服务器启动于 2025/7/10 01:05:35 ===

[错误] node:events:496
      throw er; // Unhandled 'error' event
      ^

Error: listen EADDRINUSE: address already in use :::3000
    at Server.setupListenHandle [as _listen2] (node:net:1939:16)
    at listenInCluster (node:net:1996:12)
    at Server.listen (node:net:2101:7)
    at Function.listen (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\application.js:635:24)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\qq-email-search-mcp\src\index.js:166:5)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
Emitted 'error' event on Server instance at:
    at emitErrorNT (node:net:1975:8)
    at process.processTicksAndRejections (node:internal/process/task_queues:90:21) {
  code: 'EADDRINUSE',
  errno: -4091,
  syscall: 'listen',
  address: '::',
  port: 3000
}

Node.js v22.16.0

=== 服务器退出于 2025/7/10 01:05:36，退出码: 1 ===


=== 服务器启动于 2025/7/10 01:05:54 ===

[错误] node:events:496
      throw er; // Unhandled 'error' event
      ^

Error: listen EADDRINUSE: address already in use :::3000
    at Server.setupListenHandle [as _listen2] (node:net:1939:16)
    at listenInCluster (node:net:1996:12)
    at Server.listen (node:net:2101:7)
    at Function.listen (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\application.js:635:24)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\qq-email-search-mcp\src\index.js:166:5)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
Emitted 'error' event on Server instance at:
    at emitErrorNT (node:net:1975:8)
    at process.processTicksAndRejections (node:internal/process/task_queues:90:21) {
  code: 'EADDRINUSE',
  errno: -4091,
  syscall: 'listen',
  address: '::',
  port: 3000
}

Node.js v22.16.0

=== 服务器退出于 2025/7/10 01:05:55，退出码: 1 ===


=== 服务器启动于 2025/7/10 01:06:20 ===

QQ邮箱搜索MCP服务器运行在 http://localhost:3000
可用工具: search_qq_email, export_to_excel
收到MCP工具请求: search_qq_email
[错误] 搜索邮件错误: Error: No supported authentication method(s) available. Unable to login.
    at Connection.<anonymous> (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Connection.js:1679:15)
    at Connection._resTagged (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Connection.js:1535:22)
    at Parser.<anonymous> (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Connection.js:194:10)
    at Parser.emit (node:events:518:28)
    at Parser._resTagged (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Parser.js:175:10)
    at Parser._parse (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Parser.js:139:16)
    at Parser._tryread (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Parser.js:82:15)
    at Parser._cbReadable (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Parser.js:53:12)
    at TLSSocket.emit (node:events:518:28)
    at emitReadable_ (node:internal/streams/readable:834:12) {
  source: 'authentication'
}
收到MCP工具请求: search_qq_email
[错误] 搜索邮件错误: Error: No supported authentication method(s) available. Unable to login.
    at Connection.<anonymous> (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Connection.js:1679:15)
    at Connection._resTagged (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Connection.js:1535:22)
    at Parser.<anonymous> (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Connection.js:194:10)
    at Parser.emit (node:events:518:28)
    at Parser._resTagged (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Parser.js:175:10)
    at Parser._parse (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Parser.js:139:16)
    at Parser._tryread (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Parser.js:82:15)
    at Parser._cbReadable (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Parser.js:53:12)
    at TLSSocket.emit (node:events:518:28)
    at emitReadable_ (node:internal/streams/readable:834:12) {
  source: 'authentication'
}


=== 服务器启动于 2025/7/10 08:37:14 ===

QQ邮箱搜索MCP服务器运行在 http://localhost:3000
可用工具: search_qq_email, export_to_excel
收到MCP工具请求: search_qq_email
[错误] 搜索邮件错误: Error: No supported authentication method(s) available. Unable to login.
    at Connection.<anonymous> (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Connection.js:1679:15)
    at Connection._resTagged (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Connection.js:1535:22)
    at Parser.<anonymous> (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Connection.js:194:10)
    at Parser.emit (node:events:518:28)
    at Parser._resTagged (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Parser.js:175:10)
    at Parser._parse (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Parser.js:139:16)
    at Parser._tryread (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Parser.js:82:15)
    at Parser._cbReadable (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Parser.js:53:12)
    at TLSSocket.emit (node:events:518:28)
    at emitReadable_ (node:internal/streams/readable:834:12) {
  source: 'authentication'
}


=== 服务器启动于 2025/7/10 08:45:57 ===

[错误] node:events:496
      throw er; // Unhandled 'error' event
      ^

Error: listen EADDRINUSE: address already in use :::3000
    at Server.setupListenHandle [as _listen2] (node:net:1939:16)
    at listenInCluster (node:net:1996:12)
    at Server.listen (node:net:2101:7)
    at Function.listen (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\application.js:635:24)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\qq-email-search-mcp\src\index.js:196:5)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
Emitted 'error' event on Server instance at:
    at emitErrorNT (node:net:1975:8)
    at process.processTicksAndRejections (node:internal/process/task_queues:90:21) {
  code: 'EADDRINUSE',
  errno: -4091,
  syscall: 'listen',
  address: '::',
  port: 3000
}

Node.js v22.16.0

=== 服务器退出于 2025/7/10 08:45:58，退出码: 1 ===

=== 服务器退出于 2025/7/10 08:46:25，退出码: 1 ===
