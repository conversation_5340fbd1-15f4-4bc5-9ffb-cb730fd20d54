

=== 服务器启动于 2025/7/9 23:43:42 ===


=== 服务器退出于 2025/7/9 23:43:42，退出码: null ===


=== 服务器启动于 2025/7/10 00:10:20 ===

[错误] node:events:496
      throw er; // Unhandled 'error' event
      ^

Error: listen EADDRINUSE: address already in use :::3000
    at Server.setupListenHandle [as _listen2] (node:net:1939:16)
    at listenInCluster (node:net:1996:12)
    at Server.listen (node:net:2101:7)
    at Function.listen (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\application.js:635:24)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\qq-email-search-mcp\src\index.js:161:5)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
Emitted 'error' event on Server instance at:
    at emitErrorNT (node:net:1975:8)
    at process.processTicksAndRejections (node:internal/process/task_queues:90:21) {
  code: 'EADDRINUSE',
  errno: -4091,
  syscall: 'listen',
  address: '::',
  port: 3000
}

Node.js v22.16.0

=== 服务器退出于 2025/7/10 00:10:20，退出码: 1 ===
