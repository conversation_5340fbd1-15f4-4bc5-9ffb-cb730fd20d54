

=== 服务器启动于 2025/7/9 23:43:42 ===


=== 服务器退出于 2025/7/9 23:43:42，退出码: null ===


=== 服务器启动于 2025/7/10 00:10:20 ===

[错误] node:events:496
      throw er; // Unhandled 'error' event
      ^

Error: listen EADDRINUSE: address already in use :::3000
    at Server.setupListenHandle [as _listen2] (node:net:1939:16)
    at listenInCluster (node:net:1996:12)
    at Server.listen (node:net:2101:7)
    at Function.listen (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\application.js:635:24)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\qq-email-search-mcp\src\index.js:161:5)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
Emitted 'error' event on Server instance at:
    at emitErrorNT (node:net:1975:8)
    at process.processTicksAndRejections (node:internal/process/task_queues:90:21) {
  code: 'EADDRINUSE',
  errno: -4091,
  syscall: 'listen',
  address: '::',
  port: 3000
}

Node.js v22.16.0

=== 服务器退出于 2025/7/10 00:10:20，退出码: 1 ===


=== 服务器启动于 2025/7/10 00:10:56 ===

QQ邮箱搜索MCP服务器运行在 http://localhost:3000
可用工具: search_qq_email, export_to_excel


=== 服务器启动于 2025/7/10 00:29:47 ===

[错误] node:events:496
      throw er; // Unhandled 'error' event
      ^

Error: listen EADDRINUSE: address already in use :::3000
    at Server.setupListenHandle [as _listen2] (node:net:1939:16)
    at listenInCluster (node:net:1996:12)
    at Server.listen (node:net:2101:7)
    at Function.listen (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\application.js:635:24)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\qq-email-search-mcp\src\index.js:161:5)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
Emitted 'error' event on Server instance at:
    at emitErrorNT (node:net:1975:8)
    at process.processTicksAndRejections (node:internal/process/task_queues:90:21) {
  code: 'EADDRINUSE',
  errno: -4091,
  syscall: 'listen',
  address: '::',
  port: 3000
}

Node.js v22.16.0

=== 服务器退出于 2025/7/10 00:29:48，退出码: 1 ===


=== 服务器启动于 2025/7/10 00:30:34 ===

QQ邮箱搜索MCP服务器运行在 http://localhost:3000
可用工具: search_qq_email, export_to_excel
关闭MCP服务器...

=== 服务器退出于 2025/7/10 00:33:26，退出码: null ===


=== 服务器启动于 2025/7/10 00:34:22 ===


=== 服务器退出于 2025/7/10 00:34:22，退出码: null ===


=== 服务器启动于 2025/7/10 00:35:28 ===

QQ邮箱搜索MCP服务器运行在 http://localhost:3000
可用工具: search_qq_email, export_to_excel


=== 服务器启动于 2025/7/10 00:54:39 ===

QQ邮箱搜索MCP服务器运行在 http://localhost:3000
可用工具: search_qq_email, export_to_excel


=== 服务器启动于 2025/7/10 01:01:16 ===

QQ邮箱搜索MCP服务器运行在 http://localhost:3000
可用工具: search_qq_email, export_to_excel


=== 服务器启动于 2025/7/10 01:05:35 ===

[错误] node:events:496
      throw er; // Unhandled 'error' event
      ^

Error: listen EADDRINUSE: address already in use :::3000
    at Server.setupListenHandle [as _listen2] (node:net:1939:16)
    at listenInCluster (node:net:1996:12)
    at Server.listen (node:net:2101:7)
    at Function.listen (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\application.js:635:24)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\qq-email-search-mcp\src\index.js:166:5)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
Emitted 'error' event on Server instance at:
    at emitErrorNT (node:net:1975:8)
    at process.processTicksAndRejections (node:internal/process/task_queues:90:21) {
  code: 'EADDRINUSE',
  errno: -4091,
  syscall: 'listen',
  address: '::',
  port: 3000
}

Node.js v22.16.0

=== 服务器退出于 2025/7/10 01:05:36，退出码: 1 ===


=== 服务器启动于 2025/7/10 01:05:54 ===

[错误] node:events:496
      throw er; // Unhandled 'error' event
      ^

Error: listen EADDRINUSE: address already in use :::3000
    at Server.setupListenHandle [as _listen2] (node:net:1939:16)
    at listenInCluster (node:net:1996:12)
    at Server.listen (node:net:2101:7)
    at Function.listen (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\application.js:635:24)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\qq-email-search-mcp\src\index.js:166:5)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
Emitted 'error' event on Server instance at:
    at emitErrorNT (node:net:1975:8)
    at process.processTicksAndRejections (node:internal/process/task_queues:90:21) {
  code: 'EADDRINUSE',
  errno: -4091,
  syscall: 'listen',
  address: '::',
  port: 3000
}

Node.js v22.16.0

=== 服务器退出于 2025/7/10 01:05:55，退出码: 1 ===


=== 服务器启动于 2025/7/10 01:06:20 ===

QQ邮箱搜索MCP服务器运行在 http://localhost:3000
可用工具: search_qq_email, export_to_excel
收到MCP工具请求: search_qq_email
[错误] 搜索邮件错误: Error: No supported authentication method(s) available. Unable to login.
    at Connection.<anonymous> (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Connection.js:1679:15)
    at Connection._resTagged (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Connection.js:1535:22)
    at Parser.<anonymous> (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Connection.js:194:10)
    at Parser.emit (node:events:518:28)
    at Parser._resTagged (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Parser.js:175:10)
    at Parser._parse (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Parser.js:139:16)
    at Parser._tryread (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Parser.js:82:15)
    at Parser._cbReadable (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Parser.js:53:12)
    at TLSSocket.emit (node:events:518:28)
    at emitReadable_ (node:internal/streams/readable:834:12) {
  source: 'authentication'
}
收到MCP工具请求: search_qq_email
[错误] 搜索邮件错误: Error: No supported authentication method(s) available. Unable to login.
    at Connection.<anonymous> (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Connection.js:1679:15)
    at Connection._resTagged (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Connection.js:1535:22)
    at Parser.<anonymous> (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Connection.js:194:10)
    at Parser.emit (node:events:518:28)
    at Parser._resTagged (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Parser.js:175:10)
    at Parser._parse (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Parser.js:139:16)
    at Parser._tryread (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Parser.js:82:15)
    at Parser._cbReadable (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Parser.js:53:12)
    at TLSSocket.emit (node:events:518:28)
    at emitReadable_ (node:internal/streams/readable:834:12) {
  source: 'authentication'
}


=== 服务器启动于 2025/7/10 08:37:14 ===

QQ邮箱搜索MCP服务器运行在 http://localhost:3000
可用工具: search_qq_email, export_to_excel
收到MCP工具请求: search_qq_email
[错误] 搜索邮件错误: Error: No supported authentication method(s) available. Unable to login.
    at Connection.<anonymous> (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Connection.js:1679:15)
    at Connection._resTagged (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Connection.js:1535:22)
    at Parser.<anonymous> (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Connection.js:194:10)
    at Parser.emit (node:events:518:28)
    at Parser._resTagged (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Parser.js:175:10)
    at Parser._parse (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Parser.js:139:16)
    at Parser._tryread (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Parser.js:82:15)
    at Parser._cbReadable (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Parser.js:53:12)
    at TLSSocket.emit (node:events:518:28)
    at emitReadable_ (node:internal/streams/readable:834:12) {
  source: 'authentication'
}


=== 服务器启动于 2025/7/10 08:45:57 ===

[错误] node:events:496
      throw er; // Unhandled 'error' event
      ^

Error: listen EADDRINUSE: address already in use :::3000
    at Server.setupListenHandle [as _listen2] (node:net:1939:16)
    at listenInCluster (node:net:1996:12)
    at Server.listen (node:net:2101:7)
    at Function.listen (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\application.js:635:24)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\qq-email-search-mcp\src\index.js:196:5)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
Emitted 'error' event on Server instance at:
    at emitErrorNT (node:net:1975:8)
    at process.processTicksAndRejections (node:internal/process/task_queues:90:21) {
  code: 'EADDRINUSE',
  errno: -4091,
  syscall: 'listen',
  address: '::',
  port: 3000
}

Node.js v22.16.0

=== 服务器退出于 2025/7/10 08:45:58，退出码: 1 ===

=== 服务器退出于 2025/7/10 08:46:25，退出码: 1 ===


=== 服务器启动于 2025/7/10 08:46:38 ===

QQ邮箱搜索MCP服务器运行在 http://localhost:3000
可用工具: search_qq_email, export_to_excel
收到MCP工具请求: search_qq_email
[错误] 搜索邮件错误: Error: No supported authentication method(s) available. Unable to login.
    at Connection.<anonymous> (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Connection.js:1679:15)
    at Connection._resTagged (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Connection.js:1535:22)
    at Parser.<anonymous> (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Connection.js:194:10)
    at Parser.emit (node:events:518:28)
    at Parser._resTagged (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Parser.js:175:10)
    at Parser._parse (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Parser.js:139:16)
    at Parser._tryread (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Parser.js:82:15)
    at Parser._cbReadable (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Parser.js:53:12)
    at TLSSocket.emit (node:events:518:28)
    at emitReadable_ (node:internal/streams/readable:834:12) {
  source: 'authentication'
}
开始测试QQ邮箱连接...
邮箱: <EMAIL>
授权码: vzbr****

=== 测试配置: 标准配置 ===
[connection] Connected to host
<= '* OK [CAPABILITY IMAP4 IMAP4rev1 ID AUTH=PLAIN AUTH=LOGIN AUTH=XOAUTH2 NAMESPACE] QQMail XMIMAP4Server ready'
=> 'A0 CAPABILITY'
<= '* CAPABILITY IMAP4 IMAP4rev1 XLIST MOVE IDLE XAPPLEPUSHSERVICE SASL-IR AUTH=PLAIN AUTH=LOGIN AUTH=XOAUTH2 NAMESPACE CHILDREN ID UIDPLUS'
<= 'A0 OK CAPABILITY Completed'
=> 'A1 LOGIN "<EMAIL>" "vzbropcxoztvbhea"'
<= 'A1 OK Success login ok'
=> 'A2 CAPABILITY'
<= '* CAPABILITY IMAP4 IMAP4rev1 XLIST MOVE IDLE XAPPLEPUSHSERVICE NAMESPACE CHILDREN ID UIDPLUS COMPRESS=DEFLATE'
<= 'A2 OK CAPABILITY Completed'
=> 'A3 NAMESPACE'
<= '* NAMESPACE (("" "/")) NIL NIL'
<= 'A3 OK NAMESPACE Success'
=> 'A4 LIST "" ""'
<= '* LIST (\\NoSelect) "/" "/"'
<= 'A4 OK LIST completed'
✓ 连接成功！
=> 'A5 LOGOUT'

🎉 找到可用配置: 标准配置
<= '* BYE LOGOUT received'
<= 'A5 OK LOGOUT Completed'
[connection] Ended
[connection] Closed
收到MCP工具请求: search_qq_email
[错误] 搜索邮件错误: Error: No supported authentication method(s) available. Unable to login.
    at Connection.<anonymous> (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Connection.js:1679:15)
    at Connection._resTagged (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Connection.js:1535:22)
    at Parser.<anonymous> (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Connection.js:194:10)
    at Parser.emit (node:events:518:28)
    at Parser._resTagged (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Parser.js:175:10)
    at Parser._parse (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Parser.js:139:16)
    at Parser._tryread (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Parser.js:82:15)
    at Parser._cbReadable (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\imap\lib\Parser.js:53:12)
    at TLSSocket.emit (node:events:518:28)
    at emitReadable_ (node:internal/streams/readable:834:12) {
  source: 'authentication'
}


=== 服务器启动于 2025/7/10 08:52:40 ===

QQ邮箱搜索MCP服务器运行在 http://localhost:3000
可用工具: search_qq_email, export_to_excel


=== 服务器启动于 2025/7/10 09:07:48 ===

QQ邮箱搜索MCP服务器运行在 http://localhost:3000
可用工具: search_qq_email, export_to_excel
开始nodemailer测试...
[错误] 连接测试错误: TypeError: nodemailer.createTransporter is not a function
    at testQQEmailWithNodemailer (C:\Users\<USER>\Desktop\qq-email-search-mcp\test-nodemailer.js:13:34)
    at C:\Users\<USER>\Desktop\qq-email-search-mcp\src\index.js:42:37
    at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\router\layer.js:95:5)
    at next (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\router\route.js:149:13)
    at Route.dispatch (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\router\route.js:119:3)
    at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\router\layer.js:95:5)
    at C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\router\index.js:284:15
    at Function.process_params (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\router\index.js:346:12)
    at next (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\router\index.js:280:10)
    at serveStatic (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\serve-static\index.js:75:16)
使用nodemailer测试QQ邮箱连接...
邮箱: <EMAIL>
授权码: vzbr****


=== 服务器启动于 2025/7/10 09:09:54 ===

QQ邮箱搜索MCP服务器运行在 http://localhost:3000
可用工具: search_qq_email, export_to_excel
开始nodemailer测试...
使用nodemailer测试QQ邮箱连接...
邮箱: <EMAIL>
授权码: vzbr****
[错误] 连接测试错误: TypeError: nodemailer.createTransporter is not a function
    at testQQEmailWithNodemailer (C:\Users\<USER>\Desktop\qq-email-search-mcp\test-nodemailer.js:13:34)
    at C:\Users\<USER>\Desktop\qq-email-search-mcp\src\index.js:42:37
    at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\router\layer.js:95:5)
    at next (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\router\route.js:149:13)
    at Route.dispatch (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\router\route.js:119:3)
    at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\router\layer.js:95:5)
    at C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\router\index.js:284:15
    at Function.process_params (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\router\index.js:346:12)
    at next (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\router\index.js:280:10)
    at serveStatic (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\serve-static\index.js:75:16)
收到MCP工具请求: search_qq_email
=== searchEmails 函数开始 ===
接收到的参数: {
  "email": "<EMAIL>",
  "auth_code": "vzbropcxoztvbhea",
  "protocol": "imap",
  "sender": "hxuan25 <<EMAIL>>",
  "startDate": "2025-06-10",
  "endDate": "2025-07-10"
}
准备搜索邮件，邮箱: <EMAIL>, 协议: imap, 发件人: hxuan25 <<EMAIL>>, 主题: 未指定, 日期范围: 2025-06-10 至 2025-07-10
使用IMAP协议搜索...
即将调用 searchEmailsWithIMAP...
=== 开始IMAP搜索 ===
邮箱: <EMAIL>
发件人过滤: hxuan25 <<EMAIL>>
主题过滤: 无
开始连接IMAP...
IMAP连接就绪，开始搜索...
收件箱打开成功，共有 7182 封邮件
搜索条件: [
  'ALL',
  [ 'SINCE', 2025-06-10T00:00:00.000Z ],
  [ 'BEFORE', 2025-07-10T00:00:00.000Z ],
  [ 'FROM', 'hxuan25 <<EMAIL>>' ]
]
搜索到 0 封邮件
searchEmailsWithIMAP 调用完成
IMAP连接已关闭
开始nodemailer测试...
使用nodemailer测试QQ邮箱连接...
邮箱: <EMAIL>
授权码: vzbr****
[错误] 连接测试错误: TypeError: nodemailer.createTransporter is not a function
    at testQQEmailWithNodemailer (C:\Users\<USER>\Desktop\qq-email-search-mcp\test-nodemailer.js:13:34)
    at C:\Users\<USER>\Desktop\qq-email-search-mcp\src\index.js:42:37
    at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\router\layer.js:95:5)
    at next (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\router\route.js:149:13)
    at Route.dispatch (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\router\route.js:119:3)
    at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\router\layer.js:95:5)
    at C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\router\index.js:284:15
    at Function.process_params (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\router\index.js:346:12)
    at next (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\router\index.js:280:10)
    at serveStatic (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\serve-static\index.js:75:16)


=== 服务器启动于 2025/7/10 09:12:24 ===

[错误] node:events:496
      throw er; // Unhandled 'error' event
      ^

Error: listen EADDRINUSE: address already in use :::3000
    at Server.setupListenHandle [as _listen2] (node:net:1939:16)
    at listenInCluster (node:net:1996:12)
    at Server.listen (node:net:2101:7)
    at Function.listen (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\application.js:635:24)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\qq-email-search-mcp\src\index.js:210:5)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
Emitted 'error' event on Server instance at:
    at emitErrorNT (node:net:1975:8)
    at process.processTicksAndRejections (node:internal/process/task_queues:90:21) {
  code: 'EADDRINUSE',
  errno: -4091,
  syscall: 'listen',
  address: '::',
  port: 3000
}

Node.js v22.16.0

=== 服务器退出于 2025/7/10 09:12:24，退出码: 1 ===

=== 服务器退出于 2025/7/10 09:12:57，退出码: 1 ===


=== 服务器启动于 2025/7/10 09:13:13 ===

QQ邮箱搜索MCP服务器运行在 http://localhost:3000
可用工具: search_qq_email, export_to_excel
开始nodemailer测试...
使用nodemailer测试QQ邮箱连接...
邮箱: <EMAIL>
授权码: vzbr****
[错误] 连接测试错误: TypeError: nodemailer.createTransporter is not a function
    at testQQEmailWithNodemailer (C:\Users\<USER>\Desktop\qq-email-search-mcp\test-nodemailer.js:13:34)
    at C:\Users\<USER>\Desktop\qq-email-search-mcp\src\index.js:42:37
    at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\router\layer.js:95:5)
    at next (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\router\route.js:149:13)
    at Route.dispatch (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\router\route.js:119:3)
    at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\router\layer.js:95:5)
    at C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\router\index.js:284:15
    at Function.process_params (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\router\index.js:346:12)
    at next (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\router\index.js:280:10)
    at serveStatic (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\serve-static\index.js:75:16)
开始nodemailer测试...
使用nodemailer测试QQ邮箱连接...
邮箱: <EMAIL>
授权码: vzbr****
[错误] 连接测试错误: TypeError: nodemailer.createTransporter is not a function
    at testQQEmailWithNodemailer (C:\Users\<USER>\Desktop\qq-email-search-mcp\test-nodemailer.js:13:34)
    at C:\Users\<USER>\Desktop\qq-email-search-mcp\src\index.js:42:37
    at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\router\layer.js:95:5)
    at next (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\router\route.js:149:13)
    at Route.dispatch (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\router\route.js:119:3)
    at Layer.handle [as handle_request] (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\router\layer.js:95:5)
    at C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\router\index.js:284:15
    at Function.process_params (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\router\index.js:346:12)
    at next (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\router\index.js:280:10)
    at serveStatic (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\serve-static\index.js:75:16)


=== 服务器启动于 2025/7/10 09:15:28 ===

QQ邮箱搜索MCP服务器运行在 http://localhost:3000
可用工具: search_qq_email, export_to_excel


=== 服务器启动于 2025/7/10 09:17:06 ===

QQ邮箱搜索MCP服务器运行在 http://localhost:3000
可用工具: search_qq_email, export_to_excel
开始IMAP连接测试...
IMAP连接测试成功
开始IMAP连接测试...
IMAP连接测试成功
收到MCP工具请求: search_qq_email
=== searchEmails 函数开始 ===
接收到的参数: {
  "email": "<EMAIL>",
  "auth_code": "vzbropcxoztvbhea",
  "protocol": "imap",
  "sender": "hxuan25 <<EMAIL>>",
  "startDate": "2025-06-10",
  "endDate": "2025-07-10"
}
准备搜索邮件，邮箱: <EMAIL>, 协议: imap, 发件人: hxuan25 <<EMAIL>>, 主题: 未指定, 日期范围: 2025-06-10 至 2025-07-10
使用IMAP协议搜索...
即将调用 searchEmailsWithIMAP...
=== 开始IMAP搜索 ===
邮箱: <EMAIL>
发件人过滤: hxuan25 <<EMAIL>>
主题过滤: 无
开始连接IMAP...
IMAP连接就绪，开始搜索...
收件箱打开成功，共有 7182 封邮件
搜索条件: [
  'ALL',
  [ 'SINCE', 2025-06-10T00:00:00.000Z ],
  [ 'BEFORE', 2025-07-10T00:00:00.000Z ],
  [ 'FROM', 'hxuan25 <<EMAIL>>' ]
]
搜索到 0 封邮件
searchEmailsWithIMAP 调用完成
IMAP连接已关闭
收到MCP工具请求: search_qq_email
=== searchEmails 函数开始 ===
接收到的参数: {
  "email": "<EMAIL>",
  "auth_code": "vzbropcxoztvbhea",
  "protocol": "imap",
  "sender": "hxuan25 <<EMAIL>>",
  "startDate": "2025-06-10",
  "endDate": "2025-07-10"
}
准备搜索邮件，邮箱: <EMAIL>, 协议: imap, 发件人: hxuan25 <<EMAIL>>, 主题: 未指定, 日期范围: 2025-06-10 至 2025-07-10
使用IMAP协议搜索...
即将调用 searchEmailsWithIMAP...
=== 开始IMAP搜索 ===
邮箱: <EMAIL>
发件人过滤: hxuan25 <<EMAIL>>
主题过滤: 无
开始连接IMAP...
IMAP连接就绪，开始搜索...
收件箱打开成功，共有 7182 封邮件
搜索条件: [
  'ALL',
  [ 'SINCE', 2025-06-10T00:00:00.000Z ],
  [ 'BEFORE', 2025-07-10T00:00:00.000Z ],
  [ 'FROM', 'hxuan25 <<EMAIL>>' ]
]
搜索到 0 封邮件
searchEmailsWithIMAP 调用完成
IMAP连接已关闭
收到MCP工具请求: search_qq_email
=== searchEmails 函数开始 ===
接收到的参数: {
  "email": "<EMAIL>",
  "auth_code": "vzbropcxoztvbhea",
  "protocol": "imap",
  "sender": "hxuan25 <<EMAIL>>",
  "startDate": "2025-05-01",
  "endDate": "2025-07-10"
}
准备搜索邮件，邮箱: <EMAIL>, 协议: imap, 发件人: hxuan25 <<EMAIL>>, 主题: 未指定, 日期范围: 2025-05-01 至 2025-07-10
使用IMAP协议搜索...
即将调用 searchEmailsWithIMAP...
=== 开始IMAP搜索 ===
邮箱: <EMAIL>
发件人过滤: hxuan25 <<EMAIL>>
主题过滤: 无
开始连接IMAP...
IMAP连接就绪，开始搜索...
收件箱打开成功，共有 7182 封邮件
搜索条件: [
  'ALL',
  [ 'SINCE', 2025-05-01T00:00:00.000Z ],
  [ 'BEFORE', 2025-07-10T00:00:00.000Z ],
  [ 'FROM', 'hxuan25 <<EMAIL>>' ]
]
搜索到 0 封邮件
searchEmailsWithIMAP 调用完成
IMAP连接已关闭


=== 服务器启动于 2025/7/10 09:24:47 ===

QQ邮箱搜索MCP服务器运行在 http://localhost:3000
可用工具: search_qq_email, export_to_excel
开始IMAP连接测试...
IMAP连接测试成功
收到MCP工具请求: search_qq_email
=== searchEmails 函数开始 ===
接收到的参数: {
  "email": "<EMAIL>",
  "auth_code": "vzbropcxoztvbhea",
  "protocol": "imap",
  "sender": "hxuan25 <<EMAIL>>",
  "startDate": "2025-06-10",
  "endDate": "2025-07-10"
}
准备搜索邮件，邮箱: <EMAIL>, 协议: imap, 发件人: hxuan25 <<EMAIL>>, 主题: 未指定, 日期范围: 2025-06-10 至 2025-07-10
使用IMAP协议搜索...
即将调用 searchEmailsWithIMAP...
=== 开始IMAP搜索 ===
邮箱: <EMAIL>
发件人过滤: hxuan25 <<EMAIL>>
主题过滤: 无
开始连接IMAP...
IMAP连接就绪，开始搜索...
收件箱打开成功，共有 7182 封邮件
搜索条件: [
  'ALL',
  [ 'SINCE', 2025-06-10T00:00:00.000Z ],
  [ 'BEFORE', 2025-07-10T00:00:00.000Z ],
  [ 'FROM', 'hxuan25 <<EMAIL>>' ]
]
搜索到 0 封邮件
未找到匹配邮件，建议：
1. 放宽日期范围
2. 简化发件人条件
3. 移除主题限制
searchEmailsWithIMAP 调用完成
收到MCP工具请求: search_qq_email
=== searchEmails 函数开始 ===
接收到的参数: {
  "email": "<EMAIL>",
  "auth_code": "vzbropcxoztvbhea",
  "protocol": "imap",
  "sender": "hxuan25 <<EMAIL>>"
}
准备搜索邮件，邮箱: <EMAIL>, 协议: imap, 发件人: hxuan25 <<EMAIL>>, 主题: 未指定, 日期范围: 不限 至 不限
使用IMAP协议搜索...
即将调用 searchEmailsWithIMAP...
=== 开始IMAP搜索 ===
邮箱: <EMAIL>
发件人过滤: hxuan25 <<EMAIL>>
主题过滤: 无
开始连接IMAP...
IMAP连接已关闭
IMAP连接就绪，开始搜索...
收件箱打开成功，共有 7182 封邮件
搜索条件: [ 'ALL', [ 'FROM', 'hxuan25 <<EMAIL>>' ] ]
搜索到 4 封邮件
获取最近的 4 封邮件详情...
所有邮件处理完成，共获取 4 封邮件
searchEmailsWithIMAP 调用完成
IMAP连接已关闭
收到MCP工具请求: search_qq_email
=== searchEmails 函数开始 ===
接收到的参数: {
  "email": "<EMAIL>",
  "auth_code": "vzbropcxoztvbhea",
  "protocol": "imap",
  "sender": "hxuan25 <<EMAIL>>",
  "startDate": "2025-06-10",
  "endDate": "2025-07-10"
}
准备搜索邮件，邮箱: <EMAIL>, 协议: imap, 发件人: hxuan25 <<EMAIL>>, 主题: 未指定, 日期范围: 2025-06-10 至 2025-07-10
使用IMAP协议搜索...
即将调用 searchEmailsWithIMAP...
=== 开始IMAP搜索 ===
邮箱: <EMAIL>
发件人过滤: hxuan25 <<EMAIL>>
主题过滤: 无
开始连接IMAP...
IMAP连接就绪，开始搜索...
收件箱打开成功，共有 7182 封邮件
搜索条件: [
  'ALL',
  [ 'SINCE', 2025-06-10T00:00:00.000Z ],
  [ 'BEFORE', 2025-07-10T00:00:00.000Z ],
  [ 'FROM', 'hxuan25 <<EMAIL>>' ]
]
搜索到 0 封邮件
未找到匹配邮件，建议：
1. 放宽日期范围
2. 简化发件人条件
3. 移除主题限制
searchEmailsWithIMAP 调用完成
IMAP连接已关闭
收到MCP工具请求: search_qq_email
=== searchEmails 函数开始 ===
接收到的参数: {
  "email": "<EMAIL>",
  "auth_code": "vzbropcxoztvbhea",
  "protocol": "imap",
  "sender": "hxuan25 <<EMAIL>>",
  "startDate": "2025-06-05",
  "endDate": "2025-07-10"
}
准备搜索邮件，邮箱: <EMAIL>, 协议: imap, 发件人: hxuan25 <<EMAIL>>, 主题: 未指定, 日期范围: 2025-06-05 至 2025-07-10
使用IMAP协议搜索...
即将调用 searchEmailsWithIMAP...
=== 开始IMAP搜索 ===
邮箱: <EMAIL>
发件人过滤: hxuan25 <<EMAIL>>
主题过滤: 无
开始连接IMAP...
IMAP连接就绪，开始搜索...
收件箱打开成功，共有 7182 封邮件
搜索条件: [
  'ALL',
  [ 'SINCE', 2025-06-05T00:00:00.000Z ],
  [ 'BEFORE', 2025-07-10T00:00:00.000Z ],
  [ 'FROM', 'hxuan25 <<EMAIL>>' ]
]
搜索到 0 封邮件
未找到匹配邮件，建议：
1. 放宽日期范围
2. 简化发件人条件
3. 移除主题限制
searchEmailsWithIMAP 调用完成
IMAP连接已关闭
收到MCP工具请求: search_qq_email
=== searchEmails 函数开始 ===
接收到的参数: {
  "email": "<EMAIL>",
  "auth_code": "vzbropcxoztvbhea",
  "protocol": "imap",
  "sender": "hxuan25 <<EMAIL>>",
  "startDate": "2025-06-05",
  "endDate": "2025-07-10"
}
准备搜索邮件，邮箱: <EMAIL>, 协议: imap, 发件人: hxuan25 <<EMAIL>>, 主题: 未指定, 日期范围: 2025-06-05 至 2025-07-10
使用IMAP协议搜索...
即将调用 searchEmailsWithIMAP...
=== 开始IMAP搜索 ===
邮箱: <EMAIL>
发件人过滤: hxuan25 <<EMAIL>>
主题过滤: 无
开始连接IMAP...
IMAP连接就绪，开始搜索...
收件箱打开成功，共有 7182 封邮件
搜索条件: [
  'ALL',
  [ 'SINCE', 2025-06-05T00:00:00.000Z ],
  [ 'BEFORE', 2025-07-10T00:00:00.000Z ],
  [ 'FROM', 'hxuan25 <<EMAIL>>' ]
]
搜索到 0 封邮件
未找到匹配邮件，建议：
1. 放宽日期范围
2. 简化发件人条件
3. 移除主题限制
searchEmailsWithIMAP 调用完成
IMAP连接已关闭
收到MCP工具请求: search_qq_email
=== searchEmails 函数开始 ===
接收到的参数: {
  "email": "<EMAIL>",
  "auth_code": "vzbropcxoztvbhea",
  "protocol": "imap",
  "sender": "hxuan25 <<EMAIL>>",
  "startDate": "2025-06-05",
  "endDate": "2025-07-10"
}
准备搜索邮件，邮箱: <EMAIL>, 协议: imap, 发件人: hxuan25 <<EMAIL>>, 主题: 未指定, 日期范围: 2025-06-05 至 2025-07-10
使用IMAP协议搜索...
即将调用 searchEmailsWithIMAP...
=== 开始IMAP搜索 ===
邮箱: <EMAIL>
发件人过滤: hxuan25 <<EMAIL>>
主题过滤: 无
开始连接IMAP...
IMAP连接就绪，开始搜索...
收件箱打开成功，共有 7182 封邮件
搜索条件: [
  'ALL',
  [ 'SINCE', 2025-06-05T00:00:00.000Z ],
  [ 'BEFORE', 2025-07-10T00:00:00.000Z ],
  [ 'FROM', 'hxuan25 <<EMAIL>>' ]
]
搜索到 0 封邮件
未找到匹配邮件，建议：
1. 放宽日期范围
2. 简化发件人条件
3. 移除主题限制
searchEmailsWithIMAP 调用完成
收到MCP工具请求: search_qq_email
=== searchEmails 函数开始 ===
接收到的参数: {
  "email": "<EMAIL>",
  "auth_code": "vzbropcxoztvbhea",
  "protocol": "imap",
  "sender": "hxuan25 <<EMAIL>>"
}
准备搜索邮件，邮箱: <EMAIL>, 协议: imap, 发件人: hxuan25 <<EMAIL>>, 主题: 未指定, 日期范围: 不限 至 不限
使用IMAP协议搜索...
即将调用 searchEmailsWithIMAP...
=== 开始IMAP搜索 ===
邮箱: <EMAIL>
发件人过滤: hxuan25 <<EMAIL>>
主题过滤: 无
开始连接IMAP...
IMAP连接已关闭
IMAP连接就绪，开始搜索...
收件箱打开成功，共有 7182 封邮件
搜索条件: [ 'ALL', [ 'FROM', 'hxuan25 <<EMAIL>>' ] ]
搜索到 4 封邮件
获取最近的 4 封邮件详情...
所有邮件处理完成，共获取 4 封邮件
searchEmailsWithIMAP 调用完成
IMAP连接已关闭
收到MCP工具请求: search_qq_email
=== searchEmails 函数开始 ===
接收到的参数: {
  "email": "<EMAIL>",
  "auth_code": "vzbropcxoztvbhea",
  "protocol": "imap",
  "sender": "hxuan25 <<EMAIL>>",
  "startDate": "2025-06-05",
  "endDate": "2025-07-10"
}
准备搜索邮件，邮箱: <EMAIL>, 协议: imap, 发件人: hxuan25 <<EMAIL>>, 主题: 未指定, 日期范围: 2025-06-05 至 2025-07-10
使用IMAP协议搜索...
即将调用 searchEmailsWithIMAP...
=== 开始IMAP搜索 ===
邮箱: <EMAIL>
发件人过滤: hxuan25 <<EMAIL>>
主题过滤: 无
开始连接IMAP...
IMAP连接就绪，开始搜索...
收件箱打开成功，共有 7182 封邮件
搜索条件: [
  'ALL',
  [ 'SINCE', 2025-06-05T00:00:00.000Z ],
  [ 'BEFORE', 2025-07-10T00:00:00.000Z ],
  [ 'FROM', 'hxuan25 <<EMAIL>>' ]
]
搜索到 0 封邮件
未找到匹配邮件，建议：
1. 放宽日期范围
2. 简化发件人条件
3. 移除主题限制
searchEmailsWithIMAP 调用完成
收到MCP工具请求: search_qq_email
=== searchEmails 函数开始 ===
接收到的参数: {
  "email": "<EMAIL>",
  "auth_code": "vzbropcxoztvbhea",
  "protocol": "imap",
  "sender": "hxuan25 <<EMAIL>>"
}
准备搜索邮件，邮箱: <EMAIL>, 协议: imap, 发件人: hxuan25 <<EMAIL>>, 主题: 未指定, 日期范围: 不限 至 不限
使用IMAP协议搜索...
即将调用 searchEmailsWithIMAP...
=== 开始IMAP搜索 ===
邮箱: <EMAIL>
发件人过滤: hxuan25 <<EMAIL>>
主题过滤: 无
开始连接IMAP...
IMAP连接已关闭
IMAP连接就绪，开始搜索...
收件箱打开成功，共有 7182 封邮件
搜索条件: [ 'ALL', [ 'FROM', 'hxuan25 <<EMAIL>>' ] ]
搜索到 4 封邮件
获取最近的 4 封邮件详情...
所有邮件处理完成，共获取 4 封邮件
searchEmailsWithIMAP 调用完成
IMAP连接已关闭
收到MCP工具请求: search_qq_email
=== searchEmails 函数开始 ===
接收到的参数: {
  "email": "<EMAIL>",
  "auth_code": "vzbropcxoztvbhea",
  "protocol": "imap",
  "sender": "hxuan25 <<EMAIL>>",
  "startDate": "2025-06-05",
  "endDate": "2025-07-10"
}
准备搜索邮件，邮箱: <EMAIL>, 协议: imap, 发件人: hxuan25 <<EMAIL>>, 主题: 未指定, 日期范围: 2025-06-05 至 2025-07-10
使用IMAP协议搜索...
即将调用 searchEmailsWithIMAP...
=== 开始IMAP搜索 ===
邮箱: <EMAIL>
发件人过滤: hxuan25 <<EMAIL>>
主题过滤: 无
开始连接IMAP...
IMAP连接就绪，开始搜索...
收件箱打开成功，共有 7182 封邮件
搜索条件: [
  'ALL',
  [ 'SINCE', 2025-06-05T00:00:00.000Z ],
  [ 'BEFORE', 2025-07-10T00:00:00.000Z ],
  [ 'FROM', 'hxuan25 <<EMAIL>>' ]
]
搜索到 0 封邮件
未找到匹配邮件，建议：
1. 放宽日期范围
2. 简化发件人条件
3. 移除主题限制
searchEmailsWithIMAP 调用完成
IMAP连接已关闭


=== 服务器启动于 2025/7/10 09:34:19 ===

QQ邮箱搜索MCP服务器运行在 http://localhost:3000
可用工具: search_qq_email, export_to_excel
收到MCP工具请求: search_qq_email
=== searchEmails 函数开始 ===
接收到的参数: {
  "email": "<EMAIL>",
  "auth_code": "vzbropcxoztvbhea",
  "protocol": "imap",
  "sender": "hxuan25 <<EMAIL>>",
  "startDate": "2025-06-04",
  "endDate": "2025-07-10"
}
准备搜索邮件，邮箱: <EMAIL>, 协议: imap, 发件人: hxuan25 <<EMAIL>>, 主题: 未指定, 日期范围: 2025-06-04 至 2025-07-10
使用IMAP协议搜索...
即将调用 searchEmailsWithIMAP...
=== 开始IMAP搜索 ===
邮箱: <EMAIL>
发件人过滤: hxuan25 <<EMAIL>>
主题过滤: 无
开始连接IMAP...
IMAP连接就绪，开始搜索...
收件箱打开成功，共有 7182 封邮件
添加开始日期过滤: SINCE Wed Jun 04 2025
添加结束日期过滤: BEFORE Fri Jul 11 2025 (包含原始日期: 2025-07-10)
搜索条件: [
  'ALL',
  [ 'SINCE', 2025-06-04T00:00:00.000Z ],
  [ 'BEFORE', 2025-07-11T00:00:00.000Z ],
  [ 'FROM', 'hxuan25 <<EMAIL>>' ]
]
搜索到 0 封邮件
未找到匹配邮件，建议：
1. 放宽日期范围
2. 简化发件人条件
3. 移除主题限制
searchEmailsWithIMAP 调用完成
IMAP连接已关闭


=== 服务器启动于 2025/7/10 09:47:34 ===

QQ邮箱搜索MCP服务器运行在 http://localhost:3000
可用工具: search_qq_email, export_to_excel
收到MCP工具请求: search_qq_email
=== searchEmails 函数开始 ===
接收到的参数: {
  "email": "<EMAIL>",
  "auth_code": "vzbropcxoztvbhea",
  "protocol": "imap",
  "sender": "hxuan25 <<EMAIL>>",
  "startDate": "2025-06-10",
  "endDate": "2025-07-10"
}
准备搜索邮件，邮箱: <EMAIL>, 协议: imap, 发件人: hxuan25 <<EMAIL>>, 主题: 未指定, 日期范围: 2025-06-10 至 2025-07-10
使用IMAP协议搜索...
即将调用 searchEmailsWithIMAP...
=== 开始IMAP搜索 ===
邮箱: <EMAIL>
发件人过滤: hxuan25 <<EMAIL>>
主题过滤: 无
开始连接IMAP...
IMAP连接就绪，开始搜索...
收件箱打开成功，共有 7182 封邮件
发件人邮箱地址: <EMAIL> (原始: hxuan25 <<EMAIL>>)
添加开始日期过滤: SINCE Tue Jun 10 2025
添加结束日期过滤: BEFORE Fri Jul 11 2025 (包含原始日期: 2025-07-10)
使用后过滤处理发件人条件
搜索条件: [
  [ 'SINCE', 2025-06-10T00:00:00.000Z ],
  [ 'BEFORE', 2025-07-11T00:00:00.000Z ]
]
搜索到 99 封邮件
获取最近的 50 封邮件详情...
所有邮件处理完成，共获取 50 封邮件
开始后过滤，目标发件人: <EMAIL>
✗ 发件人不匹配: =?utf-8?Q?=E4=B8=AD=E5=9B=BD=E5=9B=BD=E9=99=85=E5=8C=85=E8=A3=85?= (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?Q?swop_=E5=8C=85=E8=A3=85=E4=B8=96=E7=95=8C=28=E4=B8=8A=E6?= (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5bm/5bee5Yac5ZWG6ZO26KGM?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?gbk?B?ueO3otL40NA=?= <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✗ 发件人不匹配: Google <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?gbk?B?ueO3otL40NA=?= <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✗ 发件人不匹配: "The NordVPN team" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: NVIDIA Accounts <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✗ 发件人不匹配: =?utf-8?B?5bm/5bee5Yac5ZWG6ZO26KGM?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "Merlin Entertainments" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "The NordVPN team" <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✗ 发件人不匹配: Docker <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "The NordVPN team" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "=?UTF-8?B?QmlnTW9kZWzlvIDmlL7lubPlj7A=?=" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?UTF-8?B?5Lit5Zu95Yac5Lia6ZO26KGM?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: <EMAIL> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "invideo AI" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: npm <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?gbk?B?ueO3otL40NA=?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?q?=E9=99=88=E8=B4=9D=E5=A6=AE?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?UVHpgq7nrrHlm6LpmJ8==?= <<EMAIL>> (期望包含: <EMAIL>)
后过滤结果: 从 50 封邮件中筛选出 10 封匹配邮件
searchEmailsWithIMAP 调用完成
IMAP连接已关闭
收到MCP工具请求: export_to_excel
Excel文件已成功导出到: qq-email-search-2025-07-10T01-48-15-124Z_2025-07-10T01-48-15-129Z.xlsx
收到MCP工具请求: export_to_excel
Excel文件已成功导出到: qq-email-search-2025-07-10T01-48-34-322Z_2025-07-10T01-48-34-324Z.xlsx
收到MCP工具请求: search_qq_email
=== searchEmails 函数开始 ===
接收到的参数: {
  "email": "<EMAIL>",
  "auth_code": "vzbropcxoztvbhea",
  "protocol": "imap",
  "sender": "hxuan25 <<EMAIL>>",
  "startDate": "2025-04-28",
  "endDate": "2025-07-10"
}
准备搜索邮件，邮箱: <EMAIL>, 协议: imap, 发件人: hxuan25 <<EMAIL>>, 主题: 未指定, 日期范围: 2025-04-28 至 2025-07-10
使用IMAP协议搜索...
即将调用 searchEmailsWithIMAP...
=== 开始IMAP搜索 ===
邮箱: <EMAIL>
发件人过滤: hxuan25 <<EMAIL>>
主题过滤: 无
开始连接IMAP...
IMAP连接就绪，开始搜索...
收件箱打开成功，共有 7182 封邮件
发件人邮箱地址: <EMAIL> (原始: hxuan25 <<EMAIL>>)
添加开始日期过滤: SINCE Mon Apr 28 2025
添加结束日期过滤: BEFORE Fri Jul 11 2025 (包含原始日期: 2025-07-10)
使用后过滤处理发件人条件
搜索条件: [
  [ 'SINCE', 2025-04-28T00:00:00.000Z ],
  [ 'BEFORE', 2025-07-11T00:00:00.000Z ]
]
搜索到 237 封邮件
获取最近的 50 封邮件详情...
所有邮件处理完成，共获取 50 封邮件
开始后过滤，目标发件人: <EMAIL>
✗ 发件人不匹配: =?utf-8?Q?=E4=B8=AD=E5=9B=BD=E5=9B=BD=E9=99=85=E5=8C=85=E8=A3=85?= (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?Q?swop_=E5=8C=85=E8=A3=85=E4=B8=96=E7=95=8C=28=E4=B8=8A=E6?= (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5bm/5bee5Yac5ZWG6ZO26KGM?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?gbk?B?ueO3otL40NA=?= <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✗ 发件人不匹配: Google <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?gbk?B?ueO3otL40NA=?= <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✗ 发件人不匹配: "The NordVPN team" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: NVIDIA Accounts <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✗ 发件人不匹配: =?utf-8?B?5bm/5bee5Yac5ZWG6ZO26KGM?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "Merlin Entertainments" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "The NordVPN team" <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✗ 发件人不匹配: Docker <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "The NordVPN team" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "=?UTF-8?B?QmlnTW9kZWzlvIDmlL7lubPlj7A=?=" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?UTF-8?B?5Lit5Zu95Yac5Lia6ZO26KGM?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: <EMAIL> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "invideo AI" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: npm <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?gbk?B?ueO3otL40NA=?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?q?=E9=99=88=E8=B4=9D=E5=A6=AE?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?UVHpgq7nrrHlm6LpmJ8==?= <<EMAIL>> (期望包含: <EMAIL>)
后过滤结果: 从 50 封邮件中筛选出 10 封匹配邮件
searchEmailsWithIMAP 调用完成
IMAP连接已关闭
收到MCP工具请求: search_qq_email
=== searchEmails 函数开始 ===
接收到的参数: {
  "email": "<EMAIL>",
  "auth_code": "vzbropcxoztvbhea",
  "protocol": "imap",
  "sender": "hxuan25 <<EMAIL>>",
  "startDate": "2025-04-28",
  "endDate": "2025-07-10"
}
准备搜索邮件，邮箱: <EMAIL>, 协议: imap, 发件人: hxuan25 <<EMAIL>>, 主题: 未指定, 日期范围: 2025-04-28 至 2025-07-10
使用IMAP协议搜索...
即将调用 searchEmailsWithIMAP...
=== 开始IMAP搜索 ===
邮箱: <EMAIL>
发件人过滤: hxuan25 <<EMAIL>>
主题过滤: 无
开始连接IMAP...
IMAP连接就绪，开始搜索...
收件箱打开成功，共有 7182 封邮件
发件人邮箱地址: <EMAIL> (原始: hxuan25 <<EMAIL>>)
添加开始日期过滤: SINCE Mon Apr 28 2025
添加结束日期过滤: BEFORE Fri Jul 11 2025 (包含原始日期: 2025-07-10)
使用后过滤处理发件人条件
搜索条件: [
  [ 'SINCE', 2025-04-28T00:00:00.000Z ],
  [ 'BEFORE', 2025-07-11T00:00:00.000Z ]
]
搜索到 237 封邮件
获取最近的 50 封邮件详情...
所有邮件处理完成，共获取 50 封邮件
开始后过滤，目标发件人: <EMAIL>
✗ 发件人不匹配: =?utf-8?Q?=E4=B8=AD=E5=9B=BD=E5=9B=BD=E9=99=85=E5=8C=85=E8=A3=85?= (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?Q?swop_=E5=8C=85=E8=A3=85=E4=B8=96=E7=95=8C=28=E4=B8=8A=E6?= (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5bm/5bee5Yac5ZWG6ZO26KGM?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?gbk?B?ueO3otL40NA=?= <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✗ 发件人不匹配: Google <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?gbk?B?ueO3otL40NA=?= <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✗ 发件人不匹配: "The NordVPN team" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: NVIDIA Accounts <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✗ 发件人不匹配: =?utf-8?B?5bm/5bee5Yac5ZWG6ZO26KGM?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "Merlin Entertainments" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "The NordVPN team" <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✗ 发件人不匹配: Docker <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "The NordVPN team" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "=?UTF-8?B?QmlnTW9kZWzlvIDmlL7lubPlj7A=?=" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?UTF-8?B?5Lit5Zu95Yac5Lia6ZO26KGM?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: <EMAIL> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "invideo AI" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: npm <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?gbk?B?ueO3otL40NA=?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?q?=E9=99=88=E8=B4=9D=E5=A6=AE?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?UVHpgq7nrrHlm6LpmJ8==?= <<EMAIL>> (期望包含: <EMAIL>)
后过滤结果: 从 50 封邮件中筛选出 10 封匹配邮件
searchEmailsWithIMAP 调用完成
IMAP连接已关闭
开始IMAP连接测试...
IMAP连接测试成功
收到MCP工具请求: search_qq_email
=== searchEmails 函数开始 ===
接收到的参数: {
  "email": "<EMAIL>",
  "auth_code": "vzbropcxoztvbhea",
  "protocol": "imap",
  "sender": "hxuan25 <<EMAIL>>",
  "startDate": "2025-04-28",
  "endDate": "2025-07-10"
}
准备搜索邮件，邮箱: <EMAIL>, 协议: imap, 发件人: hxuan25 <<EMAIL>>, 主题: 未指定, 日期范围: 2025-04-28 至 2025-07-10
使用IMAP协议搜索...
即将调用 searchEmailsWithIMAP...
=== 开始IMAP搜索 ===
邮箱: <EMAIL>
发件人过滤: hxuan25 <<EMAIL>>
主题过滤: 无
开始连接IMAP...
IMAP连接就绪，开始搜索...
收件箱打开成功，共有 7182 封邮件
发件人邮箱地址: <EMAIL> (原始: hxuan25 <<EMAIL>>)
添加开始日期过滤: SINCE Mon Apr 28 2025
添加结束日期过滤: BEFORE Fri Jul 11 2025 (包含原始日期: 2025-07-10)
使用后过滤处理发件人条件
搜索条件: [
  [ 'SINCE', 2025-04-28T00:00:00.000Z ],
  [ 'BEFORE', 2025-07-11T00:00:00.000Z ]
]
搜索到 237 封邮件
获取最近的 50 封邮件详情...
所有邮件处理完成，共获取 50 封邮件
开始后过滤，目标发件人: <EMAIL>
✗ 发件人不匹配: =?utf-8?Q?=E4=B8=AD=E5=9B=BD=E5=9B=BD=E9=99=85=E5=8C=85=E8=A3=85?= (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?Q?swop_=E5=8C=85=E8=A3=85=E4=B8=96=E7=95=8C=28=E4=B8=8A=E6?= (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5bm/5bee5Yac5ZWG6ZO26KGM?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?gbk?B?ueO3otL40NA=?= <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✗ 发件人不匹配: Google <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?gbk?B?ueO3otL40NA=?= <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✗ 发件人不匹配: "The NordVPN team" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: NVIDIA Accounts <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✗ 发件人不匹配: =?utf-8?B?5bm/5bee5Yac5ZWG6ZO26KGM?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "Merlin Entertainments" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "The NordVPN team" <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✗ 发件人不匹配: Docker <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "The NordVPN team" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "=?UTF-8?B?QmlnTW9kZWzlvIDmlL7lubPlj7A=?=" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?UTF-8?B?5Lit5Zu95Yac5Lia6ZO26KGM?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: <EMAIL> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "invideo AI" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: npm <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?gbk?B?ueO3otL40NA=?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?q?=E9=99=88=E8=B4=9D=E5=A6=AE?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?UVHpgq7nrrHlm6LpmJ8==?= <<EMAIL>> (期望包含: <EMAIL>)
后过滤结果: 从 50 封邮件中筛选出 10 封匹配邮件
searchEmailsWithIMAP 调用完成
IMAP连接已关闭
收到MCP工具请求: search_qq_email
=== searchEmails 函数开始 ===
接收到的参数: {
  "email": "<EMAIL>",
  "auth_code": "vzbropcxoztvbhea",
  "protocol": "imap",
  "sender": "hxuan25 <<EMAIL>>",
  "startDate": "2025-04-28",
  "endDate": "2025-07-10"
}
准备搜索邮件，邮箱: <EMAIL>, 协议: imap, 发件人: hxuan25 <<EMAIL>>, 主题: 未指定, 日期范围: 2025-04-28 至 2025-07-10
使用IMAP协议搜索...
即将调用 searchEmailsWithIMAP...
=== 开始IMAP搜索 ===
邮箱: <EMAIL>
发件人过滤: hxuan25 <<EMAIL>>
主题过滤: 无
开始连接IMAP...
IMAP连接就绪，开始搜索...
收件箱打开成功，共有 7182 封邮件
发件人邮箱地址: <EMAIL> (原始: hxuan25 <<EMAIL>>)
添加开始日期过滤: SINCE Mon Apr 28 2025
添加结束日期过滤: BEFORE Fri Jul 11 2025 (包含原始日期: 2025-07-10)
使用后过滤处理发件人条件
搜索条件: [
  [ 'SINCE', 2025-04-28T00:00:00.000Z ],
  [ 'BEFORE', 2025-07-11T00:00:00.000Z ]
]
搜索到 237 封邮件
获取最近的 50 封邮件详情...
所有邮件处理完成，共获取 50 封邮件
开始后过滤，目标发件人: <EMAIL>
✗ 发件人不匹配: =?utf-8?Q?=E4=B8=AD=E5=9B=BD=E5=9B=BD=E9=99=85=E5=8C=85=E8=A3=85?= (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?Q?swop_=E5=8C=85=E8=A3=85=E4=B8=96=E7=95=8C=28=E4=B8=8A=E6?= (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5bm/5bee5Yac5ZWG6ZO26KGM?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?gbk?B?ueO3otL40NA=?= <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✗ 发件人不匹配: Google <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?gbk?B?ueO3otL40NA=?= <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✗ 发件人不匹配: "The NordVPN team" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: NVIDIA Accounts <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✗ 发件人不匹配: =?utf-8?B?5bm/5bee5Yac5ZWG6ZO26KGM?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "Merlin Entertainments" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "The NordVPN team" <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✗ 发件人不匹配: Docker <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "The NordVPN team" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "=?UTF-8?B?QmlnTW9kZWzlvIDmlL7lubPlj7A=?=" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?UTF-8?B?5Lit5Zu95Yac5Lia6ZO26KGM?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: <EMAIL> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "invideo AI" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: npm <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?gbk?B?ueO3otL40NA=?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?q?=E9=99=88=E8=B4=9D=E5=A6=AE?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?UVHpgq7nrrHlm6LpmJ8==?= <<EMAIL>> (期望包含: <EMAIL>)
后过滤结果: 从 50 封邮件中筛选出 10 封匹配邮件
searchEmailsWithIMAP 调用完成
IMAP连接已关闭
收到MCP工具请求: search_qq_email
=== searchEmails 函数开始 ===
接收到的参数: {
  "email": "<EMAIL>",
  "auth_code": "vzbropcxoztvbhea",
  "protocol": "imap",
  "sender": "hxuan25 <<EMAIL>>",
  "startDate": "2025-06-27",
  "endDate": "2025-07-10"
}
准备搜索邮件，邮箱: <EMAIL>, 协议: imap, 发件人: hxuan25 <<EMAIL>>, 主题: 未指定, 日期范围: 2025-06-27 至 2025-07-10
使用IMAP协议搜索...
即将调用 searchEmailsWithIMAP...
=== 开始IMAP搜索 ===
邮箱: <EMAIL>
发件人过滤: hxuan25 <<EMAIL>>
主题过滤: 无
开始连接IMAP...
IMAP连接就绪，开始搜索...
收件箱打开成功，共有 7182 封邮件
发件人邮箱地址: <EMAIL> (原始: hxuan25 <<EMAIL>>)
添加开始日期过滤: SINCE Fri Jun 27 2025
添加结束日期过滤: BEFORE Fri Jul 11 2025 (包含原始日期: 2025-07-10)
使用后过滤处理发件人条件
搜索条件: [
  [ 'SINCE', 2025-06-27T00:00:00.000Z ],
  [ 'BEFORE', 2025-07-11T00:00:00.000Z ]
]
搜索到 22 封邮件
获取最近的 22 封邮件详情...
所有邮件处理完成，共获取 22 封邮件
开始后过滤，目标发件人: <EMAIL>
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "Merlin Entertainments" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "The NordVPN team" <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✗ 发件人不匹配: Docker <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "The NordVPN team" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "=?UTF-8?B?QmlnTW9kZWzlvIDmlL7lubPlj7A=?=" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?UTF-8?B?5Lit5Zu95Yac5Lia6ZO26KGM?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: <EMAIL> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "invideo AI" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: npm <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?gbk?B?ueO3otL40NA=?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?q?=E9=99=88=E8=B4=9D=E5=A6=AE?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?UVHpgq7nrrHlm6LpmJ8==?= <<EMAIL>> (期望包含: <EMAIL>)
后过滤结果: 从 22 封邮件中筛选出 2 封匹配邮件
searchEmailsWithIMAP 调用完成
IMAP连接已关闭
收到MCP工具请求: search_qq_email
=== searchEmails 函数开始 ===
接收到的参数: {
  "email": "<EMAIL>",
  "auth_code": "vzbropcxoztvbhea",
  "protocol": "imap",
  "sender": "hxuan25 <<EMAIL>>",
  "startDate": "2025-01-27",
  "endDate": "2025-07-10"
}
准备搜索邮件，邮箱: <EMAIL>, 协议: imap, 发件人: hxuan25 <<EMAIL>>, 主题: 未指定, 日期范围: 2025-01-27 至 2025-07-10
使用IMAP协议搜索...
即将调用 searchEmailsWithIMAP...
=== 开始IMAP搜索 ===
邮箱: <EMAIL>
发件人过滤: hxuan25 <<EMAIL>>
主题过滤: 无
开始连接IMAP...
IMAP连接就绪，开始搜索...
收件箱打开成功，共有 7182 封邮件
发件人邮箱地址: <EMAIL> (原始: hxuan25 <<EMAIL>>)
添加开始日期过滤: SINCE Mon Jan 27 2025
添加结束日期过滤: BEFORE Fri Jul 11 2025 (包含原始日期: 2025-07-10)
使用后过滤处理发件人条件
搜索条件: [
  [ 'SINCE', 2025-01-27T00:00:00.000Z ],
  [ 'BEFORE', 2025-07-11T00:00:00.000Z ]
]
搜索到 424 封邮件
获取最近的 50 封邮件详情...
所有邮件处理完成，共获取 50 封邮件
开始后过滤，目标发件人: <EMAIL>
✗ 发件人不匹配: =?utf-8?Q?=E4=B8=AD=E5=9B=BD=E5=9B=BD=E9=99=85=E5=8C=85=E8=A3=85?= (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?Q?swop_=E5=8C=85=E8=A3=85=E4=B8=96=E7=95=8C=28=E4=B8=8A=E6?= (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5bm/5bee5Yac5ZWG6ZO26KGM?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?gbk?B?ueO3otL40NA=?= <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✗ 发件人不匹配: Google <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?gbk?B?ueO3otL40NA=?= <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✗ 发件人不匹配: "The NordVPN team" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: NVIDIA Accounts <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✗ 发件人不匹配: =?utf-8?B?5bm/5bee5Yac5ZWG6ZO26KGM?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "Merlin Entertainments" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "The NordVPN team" <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✗ 发件人不匹配: Docker <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "The NordVPN team" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "=?UTF-8?B?QmlnTW9kZWzlvIDmlL7lubPlj7A=?=" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?UTF-8?B?5Lit5Zu95Yac5Lia6ZO26KGM?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: <EMAIL> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "invideo AI" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: npm <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?gbk?B?ueO3otL40NA=?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?q?=E9=99=88=E8=B4=9D=E5=A6=AE?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?UVHpgq7nrrHlm6LpmJ8==?= <<EMAIL>> (期望包含: <EMAIL>)
后过滤结果: 从 50 封邮件中筛选出 10 封匹配邮件
searchEmailsWithIMAP 调用完成
IMAP连接已关闭


=== 服务器启动于 2025/7/10 09:58:36 ===

QQ邮箱搜索MCP服务器运行在 http://localhost:3000
可用工具: search_qq_email, export_to_excel


=== 服务器启动于 2025/7/10 10:00:51 ===

QQ邮箱搜索MCP服务器运行在 http://localhost:3000
可用工具: search_qq_email, export_to_excel


=== 服务器启动于 2025/7/10 10:06:00 ===

QQ邮箱搜索MCP服务器运行在 http://localhost:3000
可用工具: search_qq_email, export_to_excel
收到MCP工具请求: search_qq_email
=== searchEmails 函数开始 ===
接收到的参数: {
  "email": "<EMAIL>",
  "auth_code": "vzbropcxoztvbhea",
  "protocol": "imap",
  "sender": "hxuan25 <<EMAIL>>",
  "startDate": "2025-05-31",
  "endDate": "2025-07-10"
}
准备搜索邮件，邮箱: <EMAIL>, 协议: imap, 发件人: hxuan25 <<EMAIL>>, 主题: 未指定, 日期范围: 2025-05-31 至 2025-07-10
使用IMAP协议搜索...
即将调用 searchEmailsWithIMAP...
=== 开始IMAP搜索 ===
邮箱: <EMAIL>
发件人过滤: hxuan25 <<EMAIL>>
主题过滤: 无
开始连接IMAP...
IMAP连接就绪，开始搜索...
收件箱打开成功，共有 7182 封邮件
发件人邮箱地址: <EMAIL> (原始: hxuan25 <<EMAIL>>)
添加开始日期过滤: SINCE Sat May 31 2025
添加结束日期过滤: BEFORE Fri Jul 11 2025 (包含原始日期: 2025-07-10)
使用后过滤处理发件人条件
搜索条件: [
  [ 'SINCE', 2025-05-31T00:00:00.000Z ],
  [ 'BEFORE', 2025-07-11T00:00:00.000Z ]
]
搜索到 118 封邮件
获取最近的 118 封邮件详情... (后过滤: 是)
所有邮件处理完成，共获取 118 封邮件
开始后过滤，目标发件人: <EMAIL>
✗ 发件人不匹配: "The NordVPN team" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: Google <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: NordVPN <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "The NordVPN team" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?UTF-8?B?5Lit5Zu95Yac5Lia6ZO26KGM?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: NVIDIA <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: Apple <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: n8n.io <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: n8n.io <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: <EMAIL> (期望包含: <EMAIL>)
✗ 发件人不匹配: <EMAIL> (期望包含: <EMAIL>)
✗ 发件人不匹配: NVIDIA GTC <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "ins66.com" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: Replicate <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: Apple <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?gbk?B?ueO3otL40NA=?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: Apple <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: <EMAIL> (期望包含: <EMAIL>)
✗ 发件人不匹配: Microsoft =?utf-8?b?5biQ5oi35Zui6Zif?= (期望包含: <EMAIL>)
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: Google <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: NVIDIA GTC <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✗ 发件人不匹配: =?utf-8?Q?swop_=E5=8C=85=E8=A3=85=E4=B8=96=E7=95=8C=28=E4=B8=8A=E6?= (期望包含: <EMAIL>)
✗ 发件人不匹配: "ins66.com" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: Docker <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?Q?=E5=8D=8E=E5=8D=97=E5=8D=B0=E5=88=B7=E5=B1=95=E6=A0=87?= (期望包含: <EMAIL>)
✗ 发件人不匹配: =?gbk?B?vbvNqNL40NDQxdPDv6jW0NDE?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: Daniel at Onlook <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: Support <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: NVIDIA Webinars <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: Daniel at Onlook <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "ins66.com" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: Microsoft =?utf-8?b?5biQ5oi35Zui6Zif?= (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?Q?=E4=B8=AD=E5=9B=BD=E5=9B=BD=E9=99=85=E5=8C=85=E8=A3=85?= (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?Q?swop_=E5=8C=85=E8=A3=85=E4=B8=96=E7=95=8C=28=E4=B8=8A=E6?= (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5bm/5bee5Yac5ZWG6ZO26KGM?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?gbk?B?ueO3otL40NA=?= <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✗ 发件人不匹配: Google <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?gbk?B?ueO3otL40NA=?= <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✗ 发件人不匹配: "The NordVPN team" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: NVIDIA Accounts <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✗ 发件人不匹配: =?utf-8?B?5bm/5bee5Yac5ZWG6ZO26KGM?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "Merlin Entertainments" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "The NordVPN team" <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✗ 发件人不匹配: Docker <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "The NordVPN team" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "=?UTF-8?B?QmlnTW9kZWzlvIDmlL7lubPlj7A=?=" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?UTF-8?B?5Lit5Zu95Yac5Lia6ZO26KGM?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: <EMAIL> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "invideo AI" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: npm <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?gbk?B?ueO3otL40NA=?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?q?=E9=99=88=E8=B4=9D=E5=A6=AE?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?UVHpgq7nrrHlm6LpmJ8==?= <<EMAIL>> (期望包含: <EMAIL>)
后过滤结果: 从 118 封邮件中筛选出 18 封匹配邮件
searchEmailsWithIMAP 调用完成
IMAP连接已关闭
收到MCP工具请求: export_to_excel
Excel文件已成功导出到: qq-email-search-2025-07-10T02-07-17-210Z_2025-07-10T02-07-17-215Z.xlsx
收到MCP工具请求: search_qq_email
=== searchEmails 函数开始 ===
接收到的参数: {
  "email": "<EMAIL>",
  "auth_code": "vzbropcxoztvbhea",
  "protocol": "imap",
  "sender": "hxuan25 <<EMAIL>>",
  "startDate": "2025-06-12",
  "endDate": "2025-07-10"
}
准备搜索邮件，邮箱: <EMAIL>, 协议: imap, 发件人: hxuan25 <<EMAIL>>, 主题: 未指定, 日期范围: 2025-06-12 至 2025-07-10
使用IMAP协议搜索...
即将调用 searchEmailsWithIMAP...
=== 开始IMAP搜索 ===
邮箱: <EMAIL>
发件人过滤: hxuan25 <<EMAIL>>
主题过滤: 无
开始连接IMAP...
IMAP连接就绪，开始搜索...
收件箱打开成功，共有 7182 封邮件
发件人邮箱地址: <EMAIL> (原始: hxuan25 <<EMAIL>>)
添加开始日期过滤: SINCE Thu Jun 12 2025
添加结束日期过滤: BEFORE Fri Jul 11 2025 (包含原始日期: 2025-07-10)
使用后过滤处理发件人条件
搜索条件: [
  [ 'SINCE', 2025-06-12T00:00:00.000Z ],
  [ 'BEFORE', 2025-07-11T00:00:00.000Z ]
]
搜索到 87 封邮件
获取最近的 87 封邮件详情... (后过滤: 是)
所有邮件处理完成，共获取 87 封邮件
开始后过滤，目标发件人: <EMAIL>
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✗ 发件人不匹配: =?utf-8?Q?swop_=E5=8C=85=E8=A3=85=E4=B8=96=E7=95=8C=28=E4=B8=8A=E6?= (期望包含: <EMAIL>)
✗ 发件人不匹配: "ins66.com" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: Docker <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?Q?=E5=8D=8E=E5=8D=97=E5=8D=B0=E5=88=B7=E5=B1=95=E6=A0=87?= (期望包含: <EMAIL>)
✗ 发件人不匹配: =?gbk?B?vbvNqNL40NDQxdPDv6jW0NDE?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: Daniel at Onlook <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: Support <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: NVIDIA Webinars <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: Daniel at Onlook <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "ins66.com" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: Microsoft =?utf-8?b?5biQ5oi35Zui6Zif?= (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?Q?=E4=B8=AD=E5=9B=BD=E5=9B=BD=E9=99=85=E5=8C=85=E8=A3=85?= (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?Q?swop_=E5=8C=85=E8=A3=85=E4=B8=96=E7=95=8C=28=E4=B8=8A=E6?= (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5bm/5bee5Yac5ZWG6ZO26KGM?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?gbk?B?ueO3otL40NA=?= <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✗ 发件人不匹配: Google <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?gbk?B?ueO3otL40NA=?= <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✗ 发件人不匹配: "The NordVPN team" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: NVIDIA Accounts <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✗ 发件人不匹配: =?utf-8?B?5bm/5bee5Yac5ZWG6ZO26KGM?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "Merlin Entertainments" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "The NordVPN team" <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✗ 发件人不匹配: Docker <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "The NordVPN team" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "=?UTF-8?B?QmlnTW9kZWzlvIDmlL7lubPlj7A=?=" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?UTF-8?B?5Lit5Zu95Yac5Lia6ZO26KGM?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: <EMAIL> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "invideo AI" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: npm <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?gbk?B?ueO3otL40NA=?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?q?=E9=99=88=E8=B4=9D=E5=A6=AE?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?UVHpgq7nrrHlm6LpmJ8==?= <<EMAIL>> (期望包含: <EMAIL>)
后过滤结果: 从 87 封邮件中筛选出 16 封匹配邮件
searchEmailsWithIMAP 调用完成
IMAP连接已关闭


=== 服务器启动于 2025/7/10 10:32:27 ===

[错误] node:events:496
      throw er; // Unhandled 'error' event
      ^

Error: listen EADDRINUSE: address already in use :::3000
    at Server.setupListenHandle [as _listen2] (node:net:1939:16)
    at listenInCluster (node:net:1996:12)
    at Server.listen (node:net:2101:7)
    at Function.listen (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\application.js:635:24)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\qq-email-search-mcp\src\index.js:355:5)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
Emitted 'error' event on Server instance at:
    at emitErrorNT (node:net:1975:8)
    at process.processTicksAndRejections (node:internal/process/task_queues:90:21) {
  code: 'EADDRINUSE',
  errno: -4091,
  syscall: 'listen',
  address: '::',
  port: 3000
}

Node.js v22.16.0

=== 服务器退出于 2025/7/10 10:32:27，退出码: 1 ===


=== 服务器启动于 2025/7/10 10:32:43 ===

[错误] node:events:496
      throw er; // Unhandled 'error' event
      ^

Error: listen EADDRINUSE: address already in use :::3000
    at Server.setupListenHandle [as _listen2] (node:net:1939:16)
    at listenInCluster (node:net:1996:12)
    at Server.listen (node:net:2101:7)
    at Function.listen (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\application.js:635:24)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\qq-email-search-mcp\src\index.js:355:5)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
Emitted 'error' event on Server instance at:
    at emitErrorNT (node:net:1975:8)
    at process.processTicksAndRejections (node:internal/process/task_queues:90:21) {
  code: 'EADDRINUSE',
  errno: -4091,
  syscall: 'listen',
  address: '::',
  port: 3000
}

Node.js v22.16.0

=== 服务器退出于 2025/7/10 10:32:43，退出码: 1 ===
关闭MCP服务器...

=== 服务器退出于 2025/7/10 10:33:00，退出码: null ===


=== 服务器启动于 2025/7/10 10:33:06 ===

QQ邮箱搜索MCP服务器运行在 http://localhost:3000
可用工具: search_qq_email, export_to_excel
收到MCP工具请求: search_qq_email
=== searchEmails 函数开始 ===
接收到的参数: {
  "email": "<EMAIL>",
  "auth_code": "vzbropcxoztvbhea",
  "protocol": "imap",
  "sender": "hxuan25 <<EMAIL>>",
  "startDate": "2025-06-10",
  "endDate": "2025-07-10"
}
准备搜索邮件，邮箱: <EMAIL>, 协议: imap, 发件人: hxuan25 <<EMAIL>>, 主题: 未指定, 日期范围: 2025-06-10 至 2025-07-10
使用IMAP协议搜索...
即将调用 searchEmailsWithIMAP...
=== 开始IMAP搜索 ===
邮箱: <EMAIL>
发件人过滤: hxuan25 <<EMAIL>>
主题过滤: 无
开始连接IMAP...
IMAP连接就绪，开始搜索...
收件箱打开成功，共有 7182 封邮件
发件人邮箱地址: <EMAIL> (原始: hxuan25 <<EMAIL>>)
添加开始日期过滤: SINCE Tue Jun 10 2025
添加结束日期过滤: BEFORE Fri Jul 11 2025 (包含原始日期: 2025-07-10)
使用后过滤处理发件人条件
搜索条件: [
  [ 'SINCE', 2025-06-10T00:00:00.000Z ],
  [ 'BEFORE', 2025-07-11T00:00:00.000Z ]
]
搜索到 99 封邮件
获取最近的 99 封邮件详情... (后过滤: 是)
所有邮件处理完成，共获取 99 封邮件
开始后过滤，目标发件人: <EMAIL>
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: <EMAIL> (期望包含: <EMAIL>)
✗ 发件人不匹配: Microsoft =?utf-8?b?5biQ5oi35Zui6Zif?= (期望包含: <EMAIL>)
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: Google <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: NVIDIA GTC <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✗ 发件人不匹配: =?utf-8?Q?swop_=E5=8C=85=E8=A3=85=E4=B8=96=E7=95=8C=28=E4=B8=8A=E6?= (期望包含: <EMAIL>)
✗ 发件人不匹配: "ins66.com" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: Docker <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?Q?=E5=8D=8E=E5=8D=97=E5=8D=B0=E5=88=B7=E5=B1=95=E6=A0=87?= (期望包含: <EMAIL>)
✗ 发件人不匹配: =?gbk?B?vbvNqNL40NDQxdPDv6jW0NDE?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: Daniel at Onlook <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: Support <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: NVIDIA Webinars <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: Daniel at Onlook <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "ins66.com" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: Microsoft =?utf-8?b?5biQ5oi35Zui6Zif?= (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?Q?=E4=B8=AD=E5=9B=BD=E5=9B=BD=E9=99=85=E5=8C=85=E8=A3=85?= (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?Q?swop_=E5=8C=85=E8=A3=85=E4=B8=96=E7=95=8C=28=E4=B8=8A=E6?= (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5bm/5bee5Yac5ZWG6ZO26KGM?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?gbk?B?ueO3otL40NA=?= <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✗ 发件人不匹配: Google <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?gbk?B?ueO3otL40NA=?= <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✗ 发件人不匹配: "The NordVPN team" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: NVIDIA Accounts <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✗ 发件人不匹配: =?utf-8?B?5bm/5bee5Yac5ZWG6ZO26KGM?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "Merlin Entertainments" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "The NordVPN team" <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✗ 发件人不匹配: Docker <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "The NordVPN team" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "=?UTF-8?B?QmlnTW9kZWzlvIDmlL7lubPlj7A=?=" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?UTF-8?B?5Lit5Zu95Yac5Lia6ZO26KGM?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: <EMAIL> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "invideo AI" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: npm <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?gbk?B?ueO3otL40NA=?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?q?=E9=99=88=E8=B4=9D=E5=A6=AE?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?UVHpgq7nrrHlm6LpmJ8==?= <<EMAIL>> (期望包含: <EMAIL>)
后过滤结果: 从 99 封邮件中筛选出 18 封匹配邮件
searchEmailsWithIMAP 调用完成
IMAP连接已关闭
收到MCP工具请求: export_to_excel
Excel文件已成功导出到: qq-email-search-2025-07-10T02-34-07-417Z_2025-07-10T02-34-07-421Z.xlsx


=== 服务器启动于 2025/7/10 10:43:06 ===

[错误] node:events:496
      throw er; // Unhandled 'error' event
      ^

Error: listen EADDRINUSE: address already in use :::3000
    at Server.setupListenHandle [as _listen2] (node:net:1939:16)
    at listenInCluster (node:net:1996:12)
    at Server.listen (node:net:2101:7)
    at Function.listen (C:\Users\<USER>\Desktop\qq-email-search-mcp\node_modules\express\lib\application.js:635:24)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\qq-email-search-mcp\src\index.js:355:5)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
Emitted 'error' event on Server instance at:
    at emitErrorNT (node:net:1975:8)
    at process.processTicksAndRejections (node:internal/process/task_queues:90:21) {
  code: 'EADDRINUSE',
  errno: -4091,
  syscall: 'listen',
  address: '::',
  port: 3000
}

Node.js v22.16.0

=== 服务器退出于 2025/7/10 10:43:07，退出码: 1 ===

=== 服务器退出于 2025/7/10 10:43:41，退出码: 1 ===


=== 服务器启动于 2025/7/10 10:43:52 ===

QQ邮箱搜索MCP服务器运行在 http://localhost:3000
可用工具: search_qq_email, export_to_excel
收到MCP工具请求: search_qq_email
=== searchEmails 函数开始 ===
接收到的参数: {
  "email": "<EMAIL>",
  "auth_code": "vzbropcxoztvbhea",
  "protocol": "imap",
  "sender": "<EMAIL>",
  "startDate": "2023-11-01",
  "endDate": "2023-11-30"
}
准备搜索邮件，邮箱: <EMAIL>, 协议: imap, 发件人: <EMAIL>, 主题: 未指定, 日期范围: 2023-11-01 至 2023-11-30
使用IMAP协议搜索...
即将调用 searchEmailsWithIMAP...
=== 开始IMAP搜索 ===
邮箱: <EMAIL>
发件人过滤: <EMAIL>
主题过滤: 无
开始连接IMAP...
IMAP连接就绪，开始搜索...
收件箱打开成功，共有 7182 封邮件
发件人邮箱地址: <EMAIL> (原始: <EMAIL>)
添加开始日期过滤: SINCE Wed Nov 01 2023
添加结束日期过滤: BEFORE Fri Dec 01 2023 (包含原始日期: 2023-11-30)
使用后过滤处理发件人条件
搜索条件: [
  [ 'SINCE', 2023-11-01T00:00:00.000Z ],
  [ 'BEFORE', 2023-12-01T00:00:00.000Z ]
]
搜索到 74 封邮件
获取最近的 74 封邮件详情... (后过滤: 是)
所有邮件处理完成，共获取 74 封邮件
开始后过滤，目标发件人: <EMAIL>
✗ 发件人不匹配: AEJuice <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "=?utf-8?B?6IW+6K6v6KeG6aKR?=" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: MyVocal <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?UTF-8?B?5Lit5Zu95Yac5Lia6ZO26KGM?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: Microsoft =?utf-8?b?5biQ5oi35Zui6Zif?= (期望包含: <EMAIL>)
✗ 发件人不匹配: =?UTF-8?B?5Lic5Lqa5Lit5Zu95L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: AEJuice <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "Christine from Kaiber" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM?= <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✗ 发件人不匹配: Microsoft =?utf-8?b?5biQ5oi35Zui6Zif?= (期望包含: <EMAIL>)
✗ 发件人不匹配: Microsoft =?utf-8?b?5biQ5oi35Zui6Zif?= (期望包含: <EMAIL>)
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✗ 发件人不匹配: "=?utf-8?B?NDY2NDk0MjAz?=" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?gb18030?B?uePW3cWpyczS+NDQ?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?Q?=E5=A4=9C=E7=85=9E=E4=BA=91?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✗ 发件人不匹配: =?utf-8?Q?Leonardo.Ai?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "=?gb18030?B?uN/Jq7LK?=" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "=?gb18030?B?uN/Jq7LK?=" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: AEJuice <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?UTF-8?B?56Wo6YCa?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: AEJuice <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✗ 发件人不匹配: "Merlin Entertainments" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "=?UTF-8?B?MzPmkJzluKc=?=" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: <EMAIL> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "=?gb18030?B?NDAxNzQzMTA5?=" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: Chengsong Zhang <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: Chengsong Zhang <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✗ 发件人不匹配: =?GBK?B?0KGyyQ==?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: AEJuice <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✗ 发件人不匹配: AEJuice <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM?= <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✗ 发件人不匹配: =?gbk?B?uePW3cWpyczS+NDQ?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: AEJuice <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: AEJuice <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: AEJuice <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: AEJuice <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: AEJuice <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "=?gb18030?B?MTYxODE4Mg==?=" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: AEJuice <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: AEJuice <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "=?gb18030?B?MTYxODE4Mg==?=" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配:  (期望包含: <EMAIL>)
✗ 发件人不匹配: "Merlin Entertainments" <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✗ 发件人不匹配: "=?gb18030?B?MTYxODE4Mg==?=" <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✗ 发件人不匹配: AEJuice <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: AEJuice <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: AEJuice <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: AEJuice <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: <EMAIL>
✓ 发件人匹配: <EMAIL>
后过滤结果: 从 74 封邮件中筛选出 19 封匹配邮件
searchEmailsWithIMAP 调用完成
收到MCP工具请求: download_attachments
开始下载邮件 841 的 1 个附件...
=== 开始下载邮件 841 的附件 ===
下载目录: ./downloads/test_integration_1752115505933
附件数量: 1
创建下载目录: ./downloads/test_integration_1752115505933
IMAP连接已关闭
IMAP连接就绪，开始下载附件...
下载附件 1/1: =?gb18030?B?MjAyMzExMTYguNbH2cP7vNLPtcHQIMLt1+bSrrfyuNbH2bbA1+DS9MDWu+EgvdrEv7LhIGZpbmFsIDgwMC5wZGY=?=
获取到附件数据: 3 字节
✓ 附件下载完成: 20231116_¸ÖÇÙÃû¼ÒÏµÁÐ_Âí×æÒ®·ò¸ÖÇÙ¶À×àÒôÀÖ»á_½ÚÄ¿²á_final_800.pdf (3 字节)
所有附件处理完成，成功下载 1 个文件
下载完成，成功下载 1 个文件
收到MCP工具请求: filter_emails_with_attachments
开始筛选有附件的邮件...
=== 开始筛选有附件的邮件 ===
邮箱: <EMAIL>
最大邮件数: 50
IMAP连接就绪，开始筛选有附件的邮件...
收件箱打开成功，共有 7182 封邮件
添加发件人过滤: FROM <EMAIL>
搜索条件: [ [ 'FROM', '<EMAIL>' ] ]
搜索到 436 封邮件
获取最近的 50 封邮件详情...
✓ 邮件 6918 有 1 个附件
✓ 邮件 6919 有 1 个附件
✓ 邮件 6921 有 2 个附件
✓ 邮件 6922 有 1 个附件
✓ 邮件 6924 有 1 个附件
✓ 邮件 6930 有 1 个附件
✓ 邮件 6931 有 1 个附件
✓ 邮件 6932 有 1 个附件
✗ 邮件 6933 无附件
✓ 邮件 6934 有 2 个附件
✓ 邮件 6937 有 1 个附件
✗ 邮件 6939 无附件
✗ 邮件 6940 无附件
✓ 邮件 6944 有 1 个附件
✓ 邮件 6953 有 1 个附件
✓ 邮件 6957 有 1 个附件
✓ 邮件 6961 有 2 个附件
✓ 邮件 6963 有 1 个附件
✓ 邮件 6969 有 1 个附件
✓ 邮件 6976 有 1 个附件
✓ 邮件 6979 有 1 个附件
✗ 邮件 6980 无附件
✓ 邮件 6985 有 2 个附件
✓ 邮件 6992 有 1 个附件
✗ 邮件 6996 无附件
✗ 邮件 7002 无附件
✓ 邮件 7010 有 1 个附件
✓ 邮件 7018 有 1 个附件
✗ 邮件 7029 无附件
✓ 邮件 7035 有 2 个附件
✗ 邮件 7040 无附件
✓ 邮件 7057 有 5 个附件
✗ 邮件 7094 无附件
✓ 邮件 7095 有 1 个附件
✓ 邮件 7098 有 2 个附件
✗ 邮件 7118 无附件
✓ 邮件 7119 有 1 个附件
✗ 邮件 7125 无附件
✓ 邮件 7126 有 1 个附件
✗ 邮件 7130 无附件
✓ 邮件 7144 有 1 个附件
✓ 邮件 7147 有 1 个附件
✗ 邮件 7148 无附件
✗ 邮件 7152 无附件
✓ 邮件 7155 有 1 个附件
✓ 邮件 7156 有 1 个附件
✓ 邮件 7157 有 1 个附件
✓ 邮件 7158 有 1 个附件
✓ 邮件 7165 有 1 个附件
✓ 邮件 7168 有 1 个附件
筛选完成，找到 36 封有附件的邮件
筛选完成，找到 36 封有附件的邮件


=== 服务器启动于 2025/7/10 10:46:30 ===

QQ邮箱搜索MCP服务器运行在 http://localhost:3000
可用工具: search_qq_email, export_to_excel
收到MCP工具请求: search_qq_email
=== searchEmails 函数开始 ===
接收到的参数: {
  "email": "<EMAIL>",
  "auth_code": "vzbropcxoztvbhea",
  "protocol": "imap",
  "sender": "<EMAIL>",
  "startDate": "2023-11-01",
  "endDate": "2023-11-30"
}
准备搜索邮件，邮箱: <EMAIL>, 协议: imap, 发件人: <EMAIL>, 主题: 未指定, 日期范围: 2023-11-01 至 2023-11-30
使用IMAP协议搜索...
即将调用 searchEmailsWithIMAP...
=== 开始IMAP搜索 ===
邮箱: <EMAIL>
发件人过滤: <EMAIL>
主题过滤: 无
开始连接IMAP...
IMAP连接就绪，开始搜索...
收件箱打开成功，共有 7182 封邮件
发件人邮箱地址: <EMAIL> (原始: <EMAIL>)
添加开始日期过滤: SINCE Wed Nov 01 2023
添加结束日期过滤: BEFORE Fri Dec 01 2023 (包含原始日期: 2023-11-30)
使用后过滤处理发件人条件
搜索条件: [
  [ 'SINCE', 2023-11-01T00:00:00.000Z ],
  [ 'BEFORE', 2023-12-01T00:00:00.000Z ]
]
搜索到 74 封邮件
获取最近的 74 封邮件详情... (后过滤: 是)
所有邮件处理完成，共获取 74 封邮件
开始后过滤，目标发件人: <EMAIL>
✗ 发件人不匹配: AEJuice <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "=?utf-8?B?6IW+6K6v6KeG6aKR?=" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: MyVocal <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?UTF-8?B?5Lit5Zu95Yac5Lia6ZO26KGM?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: Microsoft =?utf-8?b?5biQ5oi35Zui6Zif?= (期望包含: <EMAIL>)
✗ 发件人不匹配: =?UTF-8?B?5Lic5Lqa5Lit5Zu95L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: AEJuice <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "Christine from Kaiber" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM?= <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✗ 发件人不匹配: Microsoft =?utf-8?b?5biQ5oi35Zui6Zif?= (期望包含: <EMAIL>)
✗ 发件人不匹配: Microsoft =?utf-8?b?5biQ5oi35Zui6Zif?= (期望包含: <EMAIL>)
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✗ 发件人不匹配: "=?utf-8?B?NDY2NDk0MjAz?=" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?gb18030?B?uePW3cWpyczS+NDQ?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?Q?=E5=A4=9C=E7=85=9E=E4=BA=91?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✗ 发件人不匹配: =?utf-8?Q?Leonardo.Ai?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "=?gb18030?B?uN/Jq7LK?=" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "=?gb18030?B?uN/Jq7LK?=" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: AEJuice <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?UTF-8?B?56Wo6YCa?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: AEJuice <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✗ 发件人不匹配: "Merlin Entertainments" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "=?UTF-8?B?MzPmkJzluKc=?=" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: <EMAIL> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "=?gb18030?B?NDAxNzQzMTA5?=" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: Chengsong Zhang <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: Chengsong Zhang <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✗ 发件人不匹配: =?GBK?B?0KGyyQ==?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: AEJuice <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✗ 发件人不匹配: AEJuice <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM?= <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✗ 发件人不匹配: =?gbk?B?uePW3cWpyczS+NDQ?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: AEJuice <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: AEJuice <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: AEJuice <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: AEJuice <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: AEJuice <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "=?gb18030?B?MTYxODE4Mg==?=" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: AEJuice <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: AEJuice <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "=?gb18030?B?MTYxODE4Mg==?=" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配:  (期望包含: <EMAIL>)
✗ 发件人不匹配: "Merlin Entertainments" <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✗ 发件人不匹配: "=?gb18030?B?MTYxODE4Mg==?=" <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✗ 发件人不匹配: AEJuice <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: AEJuice <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: AEJuice <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: AEJuice <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: <EMAIL>
✓ 发件人匹配: <EMAIL>
后过滤结果: 从 74 封邮件中筛选出 19 封匹配邮件
searchEmailsWithIMAP 调用完成
IMAP连接已关闭


=== 服务器启动于 2025/7/10 10:51:06 ===

QQ邮箱搜索MCP服务器运行在 http://localhost:3000
可用工具: search_qq_email, export_to_excel


=== 服务器启动于 2025/7/10 10:56:16 ===

QQ邮箱搜索MCP服务器运行在 http://localhost:3000
可用工具: search_qq_email, export_to_excel
收到MCP工具请求: search_qq_email
=== searchEmails 函数开始 ===
接收到的参数: {
  "email": "<EMAIL>",
  "auth_code": "vzbropcxoztvbhea",
  "protocol": "imap",
  "sender": "hxuan25 <<EMAIL>>",
  "startDate": "2025-06-10",
  "endDate": "2025-07-10"
}
准备搜索邮件，邮箱: <EMAIL>, 协议: imap, 发件人: hxuan25 <<EMAIL>>, 主题: 未指定, 日期范围: 2025-06-10 至 2025-07-10
使用IMAP协议搜索...
即将调用 searchEmailsWithIMAP...
=== 开始IMAP搜索 ===
邮箱: <EMAIL>
发件人过滤: hxuan25 <<EMAIL>>
主题过滤: 无
开始连接IMAP...
IMAP连接就绪，开始搜索...
收件箱打开成功，共有 7182 封邮件
发件人邮箱地址: <EMAIL> (原始: hxuan25 <<EMAIL>>)
添加开始日期过滤: SINCE Tue Jun 10 2025
添加结束日期过滤: BEFORE Fri Jul 11 2025 (包含原始日期: 2025-07-10)
使用后过滤处理发件人条件
搜索条件: [
  [ 'SINCE', 2025-06-10T00:00:00.000Z ],
  [ 'BEFORE', 2025-07-11T00:00:00.000Z ]
]
搜索到 99 封邮件
获取最近的 99 封邮件详情... (后过滤: 是)
所有邮件处理完成，共获取 99 封邮件
开始后过滤，目标发件人: <EMAIL>
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: <EMAIL> (期望包含: <EMAIL>)
✗ 发件人不匹配: Microsoft =?utf-8?b?5biQ5oi35Zui6Zif?= (期望包含: <EMAIL>)
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: Google <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: NVIDIA GTC <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✗ 发件人不匹配: =?utf-8?Q?swop_=E5=8C=85=E8=A3=85=E4=B8=96=E7=95=8C=28=E4=B8=8A=E6?= (期望包含: <EMAIL>)
✗ 发件人不匹配: "ins66.com" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: Docker <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?Q?=E5=8D=8E=E5=8D=97=E5=8D=B0=E5=88=B7=E5=B1=95=E6=A0=87?= (期望包含: <EMAIL>)
✗ 发件人不匹配: =?gbk?B?vbvNqNL40NDQxdPDv6jW0NDE?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: Daniel at Onlook <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: Support <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: NVIDIA Webinars <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: Daniel at Onlook <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "ins66.com" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: Microsoft =?utf-8?b?5biQ5oi35Zui6Zif?= (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?Q?=E4=B8=AD=E5=9B=BD=E5=9B=BD=E9=99=85=E5=8C=85=E8=A3=85?= (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?Q?swop_=E5=8C=85=E8=A3=85=E4=B8=96=E7=95=8C=28=E4=B8=8A=E6?= (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5bm/5bee5Yac5ZWG6ZO26KGM?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?gbk?B?ueO3otL40NA=?= <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✗ 发件人不匹配: Google <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?gbk?B?ueO3otL40NA=?= <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✗ 发件人不匹配: "The NordVPN team" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: NVIDIA Accounts <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✗ 发件人不匹配: =?utf-8?B?5bm/5bee5Yac5ZWG6ZO26KGM?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: GitHub <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "Merlin Entertainments" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "The NordVPN team" <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?gb18030?B?u8borw==?=" <<EMAIL>>
✗ 发件人不匹配: Docker <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✓ 发件人匹配: "=?utf-8?B?6buE55KH?=" <<EMAIL>>
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "The NordVPN team" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "=?UTF-8?B?QmlnTW9kZWzlvIDmlL7lubPlj7A=?=" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?UTF-8?B?5Lit5Zu95Yac5Lia6ZO26KGM?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: <EMAIL> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM5L+h55So5Y2h?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?b?5Lqs5LicSkQuY29t?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: "invideo AI" <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: npm <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?gbk?B?ueO3otL40NA=?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?5oub5ZWG6ZO26KGM?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?q?=E9=99=88=E8=B4=9D=E5=A6=AE?= <<EMAIL>> (期望包含: <EMAIL>)
✗ 发件人不匹配: =?utf-8?B?UVHpgq7nrrHlm6LpmJ8==?= <<EMAIL>> (期望包含: <EMAIL>)
后过滤结果: 从 99 封邮件中筛选出 18 封匹配邮件
searchEmailsWithIMAP 调用完成
IMAP连接已关闭
