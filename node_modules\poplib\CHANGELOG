0.1.7 (Wed Apr  2 17:41:53 MYT 2014)
    * Fixed typos

0.1.6 (Wed Mar 19 13:16:51 MYT 2014)
    * Fixed examples in README.md
    * Added MIT license to package.json
    * Addition of TLS options to pass through to underlying TLS module (thanks to <PERSON> [https://github.com/br<PERSON><PERSON><PERSON>])
    * Fixed ignoring TLS errors, bug in LIST (thanks to <PERSON> [https://github.com/brend<PERSON><PERSON>])

0.1.5 (Wed Feb 13 19:05:40 MYT 2013)

    * Fixed issue #4 (thanks to [https://github.com/AVBelyy] )
    * Fixed issue #3 (replaced references to hashlib with inbuilt crypto)
    * Better documentation, including 

0.1.4 (<PERSON><PERSON> Nov  8 11:30:36 MYT 2011):

    * Fixed issue #1 (thanks to <PERSON><PERSON> [https://github.com/nazar] )
    * Removed unused files
    * Fixed demo/demo.js
    * Fixed bug for non-match on multiline response when -ERR is received

0.1.3:

    * Fixed default value for options in constructor
    * Fixed documentation text
    * Miscellaneous minor fixes

0.1.2:

    * Removed unixlib as a dependency
    * Added CHANGELOG
