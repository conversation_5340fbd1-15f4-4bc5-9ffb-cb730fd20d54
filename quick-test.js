/**
 * 快速测试email-service的连接功能
 */
// 清除模块缓存
delete require.cache[require.resolve('./src/email-service')];
const emailService = require('./src/email-service');

async function quickTest() {
  // 使用从日志中看到的凭证进行测试
  const email = '<EMAIL>';
  const authCode = 'vzbropcxoztvbhea';
  
  console.log('开始快速测试...');
  console.log(`邮箱: ${email}`);
  console.log(`授权码: ${authCode.substring(0, 4)}****`);
  
  try {
    // 直接测试搜索功能，不单独测试连接
    console.log('\n=== 测试搜索功能 ===');
    const results = await emailService.searchEmails({
      email: email,
      auth_code: authCode,
      protocol: 'imap'
    });
    
    console.log(`✅ 搜索成功！找到 ${results.length} 封邮件`);
    
    if (results.length > 0) {
      console.log('第一封邮件信息:');
      console.log(`- 发件人: ${results[0].from}`);
      console.log(`- 主题: ${results[0].subject}`);
      console.log(`- 日期: ${results[0].date}`);
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.error('详细错误:', error);
  }
}

// 运行测试
if (require.main === module) {
  quickTest()
    .then(() => {
      console.log('\n测试完成');
      process.exit(0);
    })
    .catch(error => {
      console.error('测试过程出错:', error);
      process.exit(1);
    });
}

module.exports = { quickTest };
