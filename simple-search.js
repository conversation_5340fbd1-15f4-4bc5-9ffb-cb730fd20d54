/**
 * 简化版本的邮件搜索，用于测试
 */
const Imap = require('imap');

async function simpleEmailSearch(email, authCode) {
  console.log('开始简化邮件搜索...');
  
  // 使用已验证可用的配置
  const imap = new Imap({
    host: 'imap.qq.com',
    port: 993,
    tls: true,
    user: email,
    password: authCode,
    connTimeout: 30000,
    authTimeout: 30000
  });
  
  return new Promise((resolve, reject) => {
    const emailDetails = [];
    
    imap.once('ready', () => {
      console.log('IMAP连接就绪，开始搜索...');
      
      // 打开收件箱
      imap.openBox('INBOX', true, (err, box) => {
        if (err) {
          console.error('打开收件箱失败:', err);
          imap.end();
          reject(err);
          return;
        }
        
        console.log(`收件箱打开成功，共有 ${box.messages.total} 封邮件`);
        
        if (box.messages.total === 0) {
          console.log('收件箱为空');
          imap.end();
          resolve([]);
          return;
        }
        
        // 搜索最近的10封邮件
        const searchCriteria = ['ALL'];
        imap.search(searchCriteria, (err, results) => {
          if (err) {
            console.error('搜索失败:', err);
            imap.end();
            reject(err);
            return;
          }
          
          console.log(`搜索到 ${results.length} 封邮件`);
          
          if (results.length === 0) {
            imap.end();
            resolve([]);
            return;
          }
          
          // 只获取最近的5封邮件
          const recentResults = results.slice(-5);
          console.log(`获取最近的 ${recentResults.length} 封邮件详情...`);
          
          const fetch = imap.fetch(recentResults, {
            bodies: 'HEADER.FIELDS (FROM TO SUBJECT DATE)',
            struct: true
          });
          
          fetch.on('message', (msg, seqno) => {
            console.log(`处理邮件 ${seqno}...`);
            
            const email = {
              uid: seqno,
              from: '',
              to: '',
              subject: '',
              date: '',
              body: ''
            };
            
            msg.on('body', (stream, info) => {
              let buffer = '';
              stream.on('data', (chunk) => {
                buffer += chunk.toString('utf8');
              });
              stream.once('end', () => {
                const lines = buffer.split('\r\n');
                lines.forEach(line => {
                  if (line.toLowerCase().startsWith('from:')) {
                    email.from = line.substring(5).trim();
                  } else if (line.toLowerCase().startsWith('to:')) {
                    email.to = line.substring(3).trim();
                  } else if (line.toLowerCase().startsWith('subject:')) {
                    email.subject = line.substring(8).trim();
                  } else if (line.toLowerCase().startsWith('date:')) {
                    email.date = line.substring(5).trim();
                  }
                });
              });
            });
            
            msg.once('end', () => {
              emailDetails.push(email);
              console.log(`邮件 ${seqno} 处理完成`);
            });
          });
          
          fetch.once('error', (err) => {
            console.error('获取邮件失败:', err);
            imap.end();
            reject(err);
          });
          
          fetch.once('end', () => {
            console.log('所有邮件处理完成');
            imap.end();
            resolve(emailDetails);
          });
        });
      });
    });
    
    imap.once('error', (err) => {
      console.error('IMAP错误:', err);
      reject(err);
    });
    
    imap.once('end', () => {
      console.log('IMAP连接已关闭');
    });
    
    console.log('开始连接IMAP...');
    imap.connect();
  });
}

// 测试函数
async function testSimpleSearch() {
  const email = '<EMAIL>';
  const authCode = 'vzbropcxoztvbhea';
  
  try {
    const results = await simpleEmailSearch(email, authCode);
    console.log(`\n✅ 搜索成功！找到 ${results.length} 封邮件`);
    
    results.forEach((email, index) => {
      console.log(`\n邮件 ${index + 1}:`);
      console.log(`  发件人: ${email.from}`);
      console.log(`  主题: ${email.subject}`);
      console.log(`  日期: ${email.date}`);
    });
    
  } catch (error) {
    console.error('❌ 搜索失败:', error.message);
  }
}

// 如果直接运行此文件
if (require.main === module) {
  testSimpleSearch()
    .then(() => {
      console.log('\n简化搜索测试完成');
      process.exit(0);
    })
    .catch(error => {
      console.error('测试过程出错:', error);
      process.exit(1);
    });
}

module.exports = { simpleEmailSearch };
