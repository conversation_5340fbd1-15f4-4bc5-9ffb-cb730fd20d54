/**
 * QQ邮箱附件服务
 * 提供附件筛选和下载功能
 */

const Imap = require('imap');
const fs = require('fs');
const path = require('path');

/**
 * 筛选有附件的邮件
 */
async function filterEmailsWithAttachments(email, authCode, options = {}) {
  const { sender, subject, startDate, endDate, maxEmails = 100 } = options;
  
  console.log('=== 开始筛选有附件的邮件 ===');
  console.log(`邮箱: ${email}`);
  console.log(`最大邮件数: ${maxEmails}`);
  
  const imap = new Imap({
    host: 'imap.qq.com',
    port: 993,
    tls: true,
    user: email,
    password: authCode,
    connTimeout: 30000,
    authTimeout: 30000
  });
  
  return new Promise((resolve, reject) => {
    imap.once('ready', () => {
      console.log('IMAP连接就绪，开始筛选有附件的邮件...');
      
      imap.openBox('INBOX', true, (err, box) => {
        if (err) {
          console.error('打开收件箱失败:', err);
          imap.end();
          reject(err);
          return;
        }
        
        console.log(`收件箱打开成功，共有 ${box.messages.total} 封邮件`);
        
        // 构建搜索条件
        let searchCriteria = [];
        
        // 添加日期过滤
        if (startDate) {
          const start = new Date(startDate);
          searchCriteria.push(['SINCE', start]);
          console.log(`添加开始日期过滤: SINCE ${start.toDateString()}`);
        }
        if (endDate) {
          const end = new Date(endDate);
          end.setDate(end.getDate() + 1);
          searchCriteria.push(['BEFORE', end]);
          console.log(`添加结束日期过滤: BEFORE ${end.toDateString()}`);
        }
        
        // 添加发件人过滤
        if (sender) {
          let emailAddress = sender;
          const emailMatch = sender.match(/<([^>]+)>/);
          if (emailMatch) {
            emailAddress = emailMatch[1];
          }
          searchCriteria.push(['FROM', emailAddress]);
          console.log(`添加发件人过滤: FROM ${emailAddress}`);
        }
        
        // 添加主题过滤
        if (subject) {
          searchCriteria.push(['SUBJECT', subject]);
          console.log(`添加主题过滤: SUBJECT ${subject}`);
        }
        
        // 如果没有任何条件，使用ALL
        if (searchCriteria.length === 0) {
          searchCriteria = ['ALL'];
        }
        
        console.log('搜索条件:', searchCriteria);
        
        imap.search(searchCriteria, (err, results) => {
          if (err) {
            console.error('搜索失败:', err);
            imap.end();
            reject(err);
            return;
          }
          
          console.log(`搜索到 ${results.length} 封邮件`);
          
          if (results.length === 0) {
            console.log('未找到匹配邮件');
            imap.end();
            resolve([]);
            return;
          }
          
          // 限制邮件数量
          const limitedResults = results.slice(-maxEmails);
          console.log(`获取最近的 ${limitedResults.length} 封邮件详情...`);
          
          const fetch = imap.fetch(limitedResults, {
            bodies: 'HEADER.FIELDS (FROM TO SUBJECT DATE)',
            struct: true
          });
          
          const emailsWithAttachments = [];
          
          fetch.on('message', (msg, seqno) => {
            let emailData = {
              uid: null,
              from: '',
              to: '',
              subject: '',
              date: '',
              attachments: []
            };
            
            msg.on('body', (stream, info) => {
              let buffer = '';
              stream.on('data', (chunk) => {
                buffer += chunk.toString('utf8');
              });
              stream.once('end', () => {
                const header = Imap.parseHeader(buffer);
                emailData.from = header.from ? header.from[0] : '';
                emailData.to = header.to ? header.to[0] : '';
                emailData.subject = header.subject ? header.subject[0] : '';
                emailData.date = header.date ? header.date[0] : '';
              });
            });
            
            msg.once('attributes', (attrs) => {
              emailData.uid = attrs.uid;
              
              // 检查邮件结构中是否有附件
              if (attrs.struct) {
                const attachments = findAttachments(attrs.struct);
                emailData.attachments = attachments;
                
                // 只保留有附件的邮件
                if (attachments.length > 0) {
                  console.log(`✓ 邮件 ${seqno} 有 ${attachments.length} 个附件`);
                  emailsWithAttachments.push(emailData);
                } else {
                  console.log(`✗ 邮件 ${seqno} 无附件`);
                }
              }
            });
          });
          
          fetch.once('error', (err) => {
            console.error('获取邮件详情失败:', err);
            imap.end();
            reject(err);
          });
          
          fetch.once('end', () => {
            console.log(`筛选完成，找到 ${emailsWithAttachments.length} 封有附件的邮件`);
            imap.end();
            resolve(emailsWithAttachments);
          });
        });
      });
    });
    
    imap.once('error', (err) => {
      console.error('IMAP连接错误:', err);
      reject(err);
    });
    
    imap.connect();
  });
}

/**
 * 从邮件结构中查找附件
 */
function findAttachments(struct, attachments = [], prefix = '') {
  for (let i = 0; i < struct.length; i++) {
    const part = struct[i];
    
    if (Array.isArray(part)) {
      // 递归处理嵌套结构
      findAttachments(part, attachments, prefix + (i + 1) + '.');
    } else {
      // 检查是否为附件
      if (part.disposition && part.disposition.type === 'attachment') {
        const attachment = {
          partID: prefix + (i + 1),
          type: part.type,
          subtype: part.subtype,
          encoding: part.encoding,
          size: part.size,
          filename: part.disposition.params ? part.disposition.params.filename : 'unknown',
          contentType: `${part.type}/${part.subtype}`
        };
        
        // 如果filename在params中
        if (part.params && part.params.name) {
          attachment.filename = part.params.name;
        }
        
        attachments.push(attachment);
      }
    }
  }
  
  return attachments;
}

/**
 * 获取附件数据（基于imap-simple的逻辑）
 */
function getPartData(imap, uid, part) {
  return new Promise((resolve, reject) => {
    const fetch = imap.fetch(uid, { bodies: part.partID });

    fetch.on('message', (msg, seqno) => {
      msg.on('body', (stream, info) => {
        let buffer = '';

        stream.on('data', (chunk) => {
          buffer += chunk.toString();
        });

        stream.once('end', () => {
          try {
            let data;

            // 根据编码解码数据
            if (part.encoding === 'BASE64') {
              const cleanBase64 = buffer.replace(/[\r\n\s]/g, '');
              data = Buffer.from(cleanBase64, 'base64');
            } else if (part.encoding === 'QUOTED-PRINTABLE') {
              // 简单的Quoted-Printable解码
              const decoded = buffer.replace(/=([0-9A-F]{2})/gi, (match, hex) => {
                return String.fromCharCode(parseInt(hex, 16));
              }).replace(/=\r?\n/g, '');
              data = Buffer.from(decoded);
            } else {
              // 其他编码或无编码
              data = Buffer.from(buffer);
            }

            resolve(data);
          } catch (error) {
            reject(error);
          }
        });
      });
    });

    fetch.once('error', reject);
  });
}

/**
 * 下载指定邮件的所有附件（重写版本）
 */
async function downloadAttachments(email, authCode, uid, attachments, downloadDir) {
  console.log(`=== 开始下载邮件 ${uid} 的附件 ===`);
  console.log(`下载目录: ${downloadDir}`);
  console.log(`附件数量: ${attachments.length}`);

  // 确保下载目录存在
  if (!fs.existsSync(downloadDir)) {
    fs.mkdirSync(downloadDir, { recursive: true });
    console.log(`创建下载目录: ${downloadDir}`);
  }

  const imap = new Imap({
    host: 'imap.qq.com',
    port: 993,
    tls: true,
    user: email,
    password: authCode,
    connTimeout: 30000,
    authTimeout: 30000
  });

  return new Promise((resolve, reject) => {
    imap.once('ready', () => {
      console.log('IMAP连接就绪，开始下载附件...');

      imap.openBox('INBOX', true, async (err, box) => {
        if (err) {
          console.error('打开收件箱失败:', err);
          imap.end();
          reject(err);
          return;
        }

        try {
          const downloadedFiles = [];

          // 逐个下载附件
          for (let i = 0; i < attachments.length; i++) {
            const attachment = attachments[i];
            console.log(`下载附件 ${i + 1}/${attachments.length}: ${attachment.filename}`);

            try {
              // 获取附件数据
              const data = await getPartData(imap, uid, attachment);
              console.log(`获取到附件数据: ${data.length} 字节`);

              // 生成安全的文件名
              const safeFilename = sanitizeFilename(attachment.filename);
              const filePath = path.join(downloadDir, `${uid}_${safeFilename}`);

              // 保存文件
              fs.writeFileSync(filePath, data);

              downloadedFiles.push({
                filename: safeFilename,
                path: filePath,
                size: data.length,
                originalName: attachment.filename
              });

              console.log(`✓ 附件下载完成: ${safeFilename} (${data.length} 字节)`);

            } catch (attachmentError) {
              console.error(`下载附件失败: ${attachment.filename}`, attachmentError);
              // 继续下载其他附件
            }
          }

          console.log(`所有附件处理完成，成功下载 ${downloadedFiles.length} 个文件`);
          imap.end();
          resolve(downloadedFiles);

        } catch (error) {
          console.error('下载过程中发生错误:', error);
          imap.end();
          reject(error);
        }
      });
    });

    imap.once('error', (err) => {
      console.error('IMAP连接错误:', err);
      reject(err);
    });

    imap.connect();
  });
}

/**
 * 解码MIME编码的文件名
 */
function decodeMimeFilename(filename) {
  if (!filename) return 'unknown';

  // 处理MIME编码的文件名 (=?charset?encoding?data?=)
  const mimeRegex = /=\?([^?]+)\?([BQ])\?([^?]+)\?=/gi;

  return filename.replace(mimeRegex, (match, charset, encoding, data) => {
    try {
      if (encoding.toUpperCase() === 'B') {
        // Base64解码
        const buffer = Buffer.from(data, 'base64');

        // 处理不同编码
        if (charset.toLowerCase() === 'gb18030' || charset.toLowerCase() === 'gbk') {
          // 对于GB18030/GBK编码，尝试转换为UTF-8
          // 这里简化处理，直接使用buffer转字符串
          try {
            // 尝试使用latin1解码然后转换
            const latin1Str = buffer.toString('latin1');
            return latin1Str;
          } catch (e) {
            // 如果失败，生成一个基于时间戳的文件名
            return `attachment_${Date.now()}.pdf`;
          }
        } else {
          return buffer.toString(charset.toLowerCase() === 'utf-8' ? 'utf8' : charset.toLowerCase());
        }
      } else if (encoding.toUpperCase() === 'Q') {
        // Quoted-Printable解码
        const decoded = data.replace(/_/g, ' ').replace(/=([0-9A-F]{2})/gi, (match, hex) => {
          return String.fromCharCode(parseInt(hex, 16));
        });
        return decoded;
      }
    } catch (error) {
      console.warn('解码文件名失败:', error.message);
      // 生成一个安全的文件名
      return `attachment_${Date.now()}`;
    }
    return match; // 如果解码失败，返回原始字符串
  });
}

/**
 * 清理文件名，移除不安全字符
 */
function sanitizeFilename(filename) {
  if (!filename || filename === 'unknown') {
    return `attachment_${Date.now()}`;
  }

  // 首先解码MIME编码
  const decoded = decodeMimeFilename(filename);

  // 移除或替换不安全字符
  return decoded
    .replace(/[<>:"/\\|?*]/g, '_')
    .replace(/\s+/g, '_')
    .substring(0, 100); // 限制文件名长度
}

module.exports = {
  filterEmailsWithAttachments,
  downloadAttachments,
  findAttachments,
  decodeMimeFilename,
  sanitizeFilename
};
