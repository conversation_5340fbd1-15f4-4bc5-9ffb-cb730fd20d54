/**
 * QQ邮箱搜索MCP工具 - 邮件服务模块
 */
const Imap = require('imap');
const { simpleParser } = require('mailparser');
const util = require('util');
const POP3Client = require('poplib');

// QQ邮箱IMAP服务器配置 - 使用已验证可用的简单配置
const QQ_IMAP_CONFIGS = [
  {
    name: '可用配置',
    config: {
      host: 'imap.qq.com',
      port: 993,
      tls: true,
      connTimeout: 30000,
      authTimeout: 30000
    }
  }
];

// 默认使用第一个配置
const QQ_IMAP_CONFIG = QQ_IMAP_CONFIGS[0].config;

// QQ邮箱POP3服务器配置
const QQ_POP3_CONFIG = {
  host: 'pop.qq.com',
  port: 995,
  tls: true,
  tlsOptions: {
    rejectUnauthorized: false,  // QQ邮箱可能需要设为false
    secureProtocol: 'TLSv1_2_method'
  }
};

// QQ邮箱SMTP服务器配置（用于验证连接信息）
const QQ_SMTP_CONFIG = {
  host: 'smtp.qq.com',
  port: 465, // 也可以使用587
  secure: true, // 使用SSL
  auth: {
    // 用户名和密码将在函数中动态设置
  }
};

/**
 * 搜索邮件
 * @param {Object} options - 搜索选项
 * @param {string} options.email - QQ邮箱地址
 * @param {string} options.auth_code - QQ邮箱授权码
 * @param {string} [options.sender] - 发件人筛选
 * @param {string} [options.subject] - 主题筛选
 * @param {string} [options.startDate] - 开始日期 YYYY-MM-DD
 * @param {string} [options.endDate] - 结束日期 YYYY-MM-DD
 * @param {string} [options.protocol] - 使用的协议，可选值：'imap'或'pop3'，默认为'imap'
 * @returns {Promise<Array>} 搜索结果数组
 */
async function searchEmails(options) {
  console.log('=== searchEmails 函数开始 ===');
  console.log('接收到的参数:', JSON.stringify(options, null, 2));

  const { email, auth_code, sender, subject, startDate, endDate, protocol = 'imap' } = options;
  
  // 验证必要参数
  if (!email) {
    throw new Error('QQ邮箱地址是必需的');
  }
  
  if (!auth_code) {
    throw new Error('QQ邮箱授权码是必需的');
  }
  
  // 增强的安全验证
  if (!validateQQEmail(email)) {
    throw new Error('请提供有效的QQ邮箱地址（格式：<EMAIL>）');
  }
  
  if (!validateAuthCode(auth_code)) {
    throw new Error('授权码格式不正确，请确认是QQ邮箱的授权码');
  }
  
  console.log(`准备搜索邮件，邮箱: ${email}, 协议: ${protocol}, 发件人: ${sender || '未指定'}, 主题: ${subject || '未指定'}, 日期范围: ${startDate || '不限'} 至 ${endDate || '不限'}`);

  try {
    let results;
    // 根据选择的协议使用不同的搜索方法
    if (protocol.toLowerCase() === 'pop3') {
      console.log('使用POP3协议搜索...');
      results = await searchEmailsWithPOP3(email, auth_code, sender, subject, startDate, endDate);
    } else {
      console.log('使用IMAP协议搜索...');
      console.log('即将调用 searchEmailsWithIMAP...');
      results = await searchEmailsWithIMAP(email, auth_code, sender, subject, startDate, endDate);
      console.log('searchEmailsWithIMAP 调用完成');
    }
    
    // 搜索完成后立即清理凭证
    clearCredentials(options);
    
    return results;
  } catch (error) {
    // 确保在出现错误时也清理凭证
    clearCredentials(options);
    throw error;
  }
}

/**
 * 创建IMAP连接
 */
async function createIMAPConnection(email, auth_code) {
  console.log('创建IMAP连接...');

  const imap = new Imap({
    host: 'imap.qq.com',
    port: 993,
    tls: true,
    user: email,
    password: auth_code,
    connTimeout: 30000,
    authTimeout: 30000
  });

  return new Promise((resolve, reject) => {
    const timeout = setTimeout(() => {
      imap.end();
      reject(new Error('连接超时'));
    }, 30000);

    imap.once('ready', () => {
      clearTimeout(timeout);
      console.log('✓ IMAP连接成功');
      resolve(imap);
    });

    imap.once('error', (err) => {
      clearTimeout(timeout);
      console.log(`✗ IMAP连接失败: ${err.message}`);
      reject(err);
    });

    console.log('开始连接IMAP...');
    imap.connect();
  });
}

// 保持向后兼容
async function tryIMAPConnection(email, auth_code, configIndex = 0, keepAlive = false) {
  return createIMAPConnection(email, auth_code);
}

/**
 * 使用IMAP协议搜索邮件 - 简化版本
 */
async function searchEmailsWithIMAP(email, auth_code, sender, subject, startDate, endDate) {
  console.log('=== 开始IMAP搜索 ===');
  console.log(`邮箱: ${email}`);
  console.log(`发件人过滤: ${sender || '无'}`);
  console.log(`主题过滤: ${subject || '无'}`);

  const imap = new Imap({
    host: 'imap.qq.com',
    port: 993,
    tls: true,
    user: email,
    password: auth_code,
    connTimeout: 30000,
    authTimeout: 30000
  });

  return new Promise((resolve, reject) => {
    const emailDetails = [];

    imap.once('ready', () => {
      console.log('IMAP连接就绪，开始搜索...');

      // 打开收件箱
      imap.openBox('INBOX', true, (err, box) => {
        if (err) {
          console.error('打开收件箱失败:', err);
          imap.end();
          reject(err);
          return;
        }

        console.log(`收件箱打开成功，共有 ${box.messages.total} 封邮件`);

        if (box.messages.total === 0) {
          console.log('收件箱为空');
          imap.end();
          resolve([]);
          return;
        }

        // 构建搜索条件
        let searchCriteria = ['ALL'];

        // 添加日期过滤 - 修复SINCE+BEFORE逻辑
        if (startDate) {
          const start = new Date(startDate);
          searchCriteria.push(['SINCE', start]);
          console.log(`添加开始日期过滤: SINCE ${start.toDateString()}`);
        }
        if (endDate) {
          // BEFORE是不包含指定日期的，所以要加1天来包含结束日期
          const end = new Date(endDate);
          end.setDate(end.getDate() + 1); // 加1天，使BEFORE包含结束日期
          searchCriteria.push(['BEFORE', end]);
          console.log(`添加结束日期过滤: BEFORE ${end.toDateString()} (包含原始日期: ${endDate})`);
        }

        // 添加发件人过滤
        if (sender) {
          searchCriteria.push(['FROM', sender]);
        }

        // 添加主题过滤
        if (subject) {
          searchCriteria.push(['SUBJECT', subject]);
        }

        console.log('搜索条件:', searchCriteria);

        imap.search(searchCriteria, (err, results) => {
          if (err) {
            console.error('搜索失败:', err);
            imap.end();
            reject(err);
            return;
          }

          console.log(`搜索到 ${results.length} 封邮件`);

          if (results.length === 0) {
            console.log('未找到匹配邮件，建议：');
            console.log('1. 放宽日期范围');
            console.log('2. 简化发件人条件');
            console.log('3. 移除主题限制');
            imap.end();
            resolve([]);
            return;
          }

          // 限制结果数量，避免处理过多邮件
          const limitedResults = results.slice(-50); // 最多50封
          console.log(`获取最近的 ${limitedResults.length} 封邮件详情...`);

          const fetch = imap.fetch(limitedResults, {
            bodies: 'HEADER.FIELDS (FROM TO SUBJECT DATE)',
            struct: true
          });

          fetch.on('message', (msg, seqno) => {
            const email = {
              uid: seqno,
              from: '',
              to: '',
              subject: '',
              date: '',
              body: ''
            };

            msg.on('body', (stream, info) => {
              let buffer = '';
              stream.on('data', (chunk) => {
                buffer += chunk.toString('utf8');
              });
              stream.once('end', () => {
                const lines = buffer.split('\r\n');
                lines.forEach(line => {
                  if (line.toLowerCase().startsWith('from:')) {
                    email.from = line.substring(5).trim();
                  } else if (line.toLowerCase().startsWith('to:')) {
                    email.to = line.substring(3).trim();
                  } else if (line.toLowerCase().startsWith('subject:')) {
                    email.subject = line.substring(8).trim();
                  } else if (line.toLowerCase().startsWith('date:')) {
                    email.date = line.substring(5).trim();
                  }
                });
              });
            });

            msg.once('end', () => {
              emailDetails.push(email);
            });
          });

          fetch.once('error', (err) => {
            console.error('获取邮件失败:', err);
            imap.end();
            reject(err);
          });

          fetch.once('end', () => {
            console.log(`所有邮件处理完成，共获取 ${emailDetails.length} 封邮件`);
            imap.end();
            resolve(emailDetails);
          });
        });
      });
    });

    imap.once('error', (err) => {
      console.error('IMAP错误:', err);
      reject(err);
    });

    imap.once('end', () => {
      console.log('IMAP连接已关闭');
    });

    console.log('开始连接IMAP...');
    imap.connect();
  });
}


/**
 * 使用POP3协议搜索邮件
 */
async function searchEmailsWithPOP3(email, auth_code, sender, subject, startDate, endDate) {
  console.log('使用POP3协议搜索邮件...');
  
  // 尝试多次连接，以应对可能的网络问题
  let retryCount = 0;
  const maxRetries = 3;
  
  while (retryCount < maxRetries) {
    // 创建POP3客户端
    const pop3Client = new POP3Client(
      QQ_POP3_CONFIG.port,
      QQ_POP3_CONFIG.host,
      {
        tlserrs: false,
        enabletls: QQ_POP3_CONFIG.tls,
        debug: false
      }
    );
    
    try {
      console.log(`尝试连接到QQ邮箱POP3服务器 (尝试 ${retryCount + 1}/${maxRetries})...`);
      
      // 连接并认证
      await new Promise((resolve, reject) => {
        // 连接事件
        pop3Client.on('connect', () => {
          console.log('已连接到QQ邮箱POP3服务器');
          
          // 认证
          pop3Client.login(email, auth_code);
        });
        
        // 登录成功事件
        pop3Client.on('login', (status) => {
          if (status) {
            console.log('POP3登录成功');
            resolve();
          } else {
            reject(new Error('POP3登录失败：邮箱地址或授权码不正确'));
          }
        });
        
        // 错误事件
        pop3Client.on('error', (err) => {
          reject(new Error(`POP3连接错误：${err.message || err}`));
        });
      });
      
      // 获取邮件列表
      const messageCount = await new Promise((resolve, reject) => {
        pop3Client.stat((err, count, size) => {
          if (err) {
            reject(new Error(`获取邮件统计信息失败：${err.message || err}`));
            return;
          }
          console.log(`邮箱中共有 ${count} 封邮件，总大小 ${size} 字节`);
          resolve(count);
        });
      });
      
      if (messageCount === 0) {
        console.log('邮箱中没有邮件');
        pop3Client.quit();
        return [];
      }
      
      // 获取邮件列表
      const messageList = await new Promise((resolve, reject) => {
        pop3Client.list((err, messages) => {
          if (err) {
            reject(new Error(`获取邮件列表失败：${err.message || err}`));
            return;
          }
          resolve(messages);
        });
      });
      
      console.log(`获取到 ${messageList.length} 封邮件的信息`);
      
      // 限制处理的邮件数量，避免一次处理太多
      const limitedList = messageList.slice(Math.max(0, messageList.length - 50));
      console.log(`将处理最新的 ${limitedList.length} 封邮件`);
      
      // 获取邮件详情
      const emails = [];
      
      for (const message of limitedList) {
        const messageNumber = message.number;
        try {
          // 获取邮件内容
          const rawEmail = await new Promise((resolve, reject) => {
            pop3Client.retr(messageNumber, (err, lines) => {
              if (err) {
                reject(new Error(`获取邮件 ${messageNumber} 内容失败：${err.message || err}`));
                return;
              }
              resolve(lines.join('\r\n'));
            });
          });
          
          // 解析邮件内容
          const parsedEmail = await simpleParser(rawEmail);
          
          // 创建邮件对象
          const email = {
            id: messageNumber,
            messageId: parsedEmail.messageId,
            from: parsedEmail.from ? parsedEmail.from.text : '',
            to: parsedEmail.to ? parsedEmail.to.text : '',
            subject: parsedEmail.subject || '',
            date: parsedEmail.date ? parsedEmail.date.toISOString() : '',
            text: parsedEmail.text || '',
            html: parsedEmail.html || '',
            attachments: []
          };
          
          // 处理附件信息
          if (parsedEmail.attachments && parsedEmail.attachments.length > 0) {
            email.attachments = parsedEmail.attachments.map(attachment => ({
              filename: attachment.filename,
              contentType: attachment.contentType,
              size: attachment.size
            }));
          }
          
          // 应用过滤条件
          let matchesSender = true;
          let matchesSubject = true;
          let matchesDateRange = true;
          
          // 检查发件人
          if (sender && email.from) {
            matchesSender = email.from.toLowerCase().includes(sender.toLowerCase());
          }
          
          // 检查主题
          if (subject && email.subject) {
            matchesSubject = email.subject.toLowerCase().includes(subject.toLowerCase());
          }
          
          // 检查日期范围
          if (startDate && email.date) {
            const emailDate = new Date(email.date);
            const startDateObj = new Date(startDate);
            matchesDateRange = emailDate >= startDateObj;
          }
          
          if (endDate && email.date) {
            const emailDate = new Date(email.date);
            const endDateObj = new Date(endDate);
            // 添加一天，使其包含当天
            endDateObj.setDate(endDateObj.getDate() + 1);
            matchesDateRange = matchesDateRange && emailDate <= endDateObj;
          }
          
          // 如果满足所有条件，添加到结果中
          if (matchesSender && matchesSubject && matchesDateRange) {
            emails.push(email);
          }
        } catch (error) {
          console.error(`处理邮件 ${messageNumber} 时出错:`, error);
        }
      }
      
      // 关闭连接
      pop3Client.quit();
      
      console.log(`POP3搜索完成，找到 ${emails.length} 封匹配的邮件`);
      return emails;
    } catch (error) {
      console.error(`POP3尝试 ${retryCount + 1}/${maxRetries} 失败:`, error);
      
      // 确保连接关闭
      try {
        pop3Client.quit();
      } catch (e) {
        // 忽略关闭连接时的错误
      }
      
      // 如果是认证错误，不再重试
      if (error.message.includes('邮箱地址或授权码不正确')) {
        throw error;
      }
      
      // 如果已经尝试了最大次数，抛出错误
      if (retryCount === maxRetries - 1) {
        throw error;
      }
      
      // 增加重试计数并等待一段时间后重试
      retryCount++;
      console.log(`将在 3 秒后进行第 ${retryCount + 1} 次尝试...`);
      await new Promise(resolve => setTimeout(resolve, 3000));
    }
  }
  
  // 如果所有重试都失败，抛出一个通用错误
  throw new Error('无法连接到QQ邮箱POP3服务器，请检查网络连接和POP3服务是否已开启');
}

/**
 * 构建IMAP搜索条件
 */
function buildSearchCriteria(sender, subject, startDate, endDate) {
  const criteria = [];
  
  // 添加发件人条件
  if (sender) {
    console.log(`添加发件人条件: ${sender}`);
    criteria.push(['FROM', sender]);
  }
  
  // 添加主题条件
  if (subject) {
    console.log(`添加主题条件: ${subject}`);
    criteria.push(['SUBJECT', subject]);
  }
  
  // 添加日期范围条件
  if (startDate) {
    try {
      // 确保日期格式正确
      let startDateObj;
      if (typeof startDate === 'string' && startDate.match(/^\d{4}-\d{2}-\d{2}$/)) {
        // 如果是YYYY-MM-DD格式，确保使用UTC时间
        const [year, month, day] = startDate.split('-').map(Number);
        startDateObj = new Date(Date.UTC(year, month - 1, day));
      } else {
        startDateObj = new Date(startDate);
      }
      
      if (isNaN(startDateObj.getTime())) {
        console.error(`无效的开始日期: ${startDate}`);
      } else {
        console.log(`添加开始日期条件: ${startDateObj.toISOString()}`);
        criteria.push(['SINCE', startDateObj]);
      }
    } catch (error) {
      console.error(`处理开始日期时出错: ${error.message}`);
    }
  }
  
  if (endDate) {
    try {
      // 确保日期格式正确
      let endDateObj;
      if (typeof endDate === 'string' && endDate.match(/^\d{4}-\d{2}-\d{2}$/)) {
        // 如果是YYYY-MM-DD格式，确保使用UTC时间
        const [year, month, day] = endDate.split('-').map(Number);
        endDateObj = new Date(Date.UTC(year, month - 1, day));
      } else {
        endDateObj = new Date(endDate);
      }
      
      if (isNaN(endDateObj.getTime())) {
        console.error(`无效的结束日期: ${endDate}`);
      } else {
        // 添加一天，因为BEFORE是严格的"早于"，不包括当天
        const nextDay = new Date(endDateObj.getTime() + 24 * 60 * 60 * 1000);
        console.log(`添加结束日期条件: ${nextDay.toISOString()}`);
        criteria.push(['BEFORE', nextDay]);
      }
    } catch (error) {
      console.error(`处理结束日期时出错: ${error.message}`);
    }
  }
  
  // 如果没有任何条件，默认搜索最近30天的邮件，缩短搜索范围以提高成功率
  if (criteria.length === 0) {
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    console.log(`没有提供搜索条件，默认搜索最近30天的邮件: ${thirtyDaysAgo.toISOString()}`);
    return ['SINCE', thirtyDaysAgo];
  }
  
  // 组合多个条件
  if (criteria.length === 1) {
    return criteria[0];
  } else {
    // IMAP库不支持['AND', ...criteria]格式，需要直接返回条件数组
    return criteria;
  }
}

/**
 * 搜索邮件
 */
function searchMessages(imap, criteria) {
  console.log('正在执行搜索，条件:', JSON.stringify(criteria));
  return new Promise((resolve, reject) => {
    try {
      // 尝试使用ALL搜索条件测试连接
      imap.search(['ALL'], (testErr, testResults) => {
        if (testErr) {
          console.error('测试搜索ALL失败:', testErr.message);
          reject(new Error(`IMAP搜索测试失败: ${testErr.message}`));
          return;
        }
        
        console.log(`测试搜索成功，邮箱中共有 ${testResults.length} 封邮件`);
        
        // 处理搜索条件，确保格式正确
        let searchCriteria = criteria;
        
        // 如果是发件人搜索，需要特殊处理
        if (Array.isArray(criteria) && criteria.length > 0) {
          for (let i = 0; i < criteria.length; i++) {
            if (Array.isArray(criteria[i]) && criteria[i][0] === 'FROM') {
              const sender = criteria[i][1];
              // 如果发件人包含中文或特殊格式，提取邮箱地址
              if (/[\u4e00-\u9fa5<>]/.test(sender)) {
                const emailMatch = sender.match(/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/);
                if (emailMatch) {
                  const email = emailMatch[0];
                  console.log(`发件人包含特殊字符，提取邮箱地址: ${email}`);
                  searchCriteria[i][1] = email;
                }
              }
            }
          }
        }
        
  // 如果测试成功，执行实际搜索
  console.log('使用处理后的搜索条件:', JSON.stringify(searchCriteria));
  
  // 首先按日期范围搜索
  let dateSearchCriteria = ['ALL'];
  
  // 提取日期条件
  if (Array.isArray(searchCriteria)) {
    const dateConditions = [];
    for (const criterion of searchCriteria) {
      if (Array.isArray(criterion) && (criterion[0] === 'SINCE' || criterion[0] === 'BEFORE')) {
        dateConditions.push(criterion);
      }
    }
    
    if (dateConditions.length > 0) {
      dateSearchCriteria = dateConditions;
    }
  }
  
  console.log('日期搜索条件:', JSON.stringify(dateSearchCriteria));
  
  imap.search(dateSearchCriteria, (err, results) => {
    if (err) {
      console.error('搜索出错:', err.message);
      reject(err);
      return;
    }
    console.log(`搜索完成，找到 ${results.length} 个结果`);
    resolve(results);
  });
      });
    } catch (error) {
      console.error('执行搜索时发生异常:', error);
      reject(error);
    }
  });
}

/**
 * 获取邮件详情并应用搜索条件
 */
async function fetchEmailDetails(imap, messageIds, searchCriteria) {
  // 获取原始搜索条件
  const originalCriteria = searchCriteria || [];
  let senderFilter = '';
  let subjectFilter = '';
  let startDateFilter = null;
  let endDateFilter = null;
  
  // 提取搜索条件
  if (Array.isArray(originalCriteria)) {
    for (const criterion of originalCriteria) {
      if (Array.isArray(criterion)) {
        if (criterion[0] === 'FROM') {
          senderFilter = criterion[1];
          // 如果发件人包含中文或特殊格式，提取邮箱地址
          if (/[\u4e00-\u9fa5<>]/.test(senderFilter)) {
            const emailMatch = senderFilter.match(/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/);
            if (emailMatch) {
              senderFilter = emailMatch[0];
            }
          }
        } else if (criterion[0] === 'SUBJECT') {
          subjectFilter = criterion[1];
        } else if (criterion[0] === 'SINCE') {
          startDateFilter = criterion[1];
        } else if (criterion[0] === 'BEFORE') {
          endDateFilter = criterion[1];
        }
      }
    }
  }
  
  console.log(`应用客户端过滤条件 - 发件人: ${senderFilter || '无'}, 主题: ${subjectFilter || '无'}, 开始日期: ${startDateFilter ? startDateFilter.toISOString() : '无'}, 结束日期: ${endDateFilter ? endDateFilter.toISOString() : '无'}`);
  
  // 限制处理的邮件数量，避免一次处理太多
  const limitedIds = messageIds.slice(0, 100); // 增加处理的邮件数量，以便找到更多匹配的邮件
  const emails = [];
  
  // 如果没有找到邮件，返回空数组
  if (limitedIds.length === 0) {
    console.log('没有找到邮件，返回空结果');
    return emails;
  }
  
  console.log(`正在获取 ${limitedIds.length} 封邮件的详情...`);
  
  return new Promise((resolve, reject) => {
    try {
      const fetch = imap.fetch(limitedIds, {
        bodies: '',
        struct: true
      });
      
      let processedCount = 0;
      
      fetch.on('message', (msg, seqno) => {
        console.log(`处理第 ${seqno} 封邮件...`);
        
        const email = {
          id: seqno,
          messageId: '',
          from: '',
          to: '',
          subject: '',
          date: '',
          text: '',
          html: '',
          attachments: []
        };
        
        msg.on('body', (stream) => {
          let buffer = '';
          
          stream.on('data', (chunk) => {
            buffer += chunk.toString('utf8');
          });
          
          stream.once('end', () => {
            // 解析邮件内容
            simpleParser(buffer)
              .then(parsed => {
                email.messageId = parsed.messageId;
                email.from = parsed.from ? parsed.from.text : '';
                email.to = parsed.to ? parsed.to.text : '';
                email.subject = parsed.subject || '';
                email.date = parsed.date ? parsed.date.toISOString() : '';
                email.text = parsed.text || '';
                email.html = parsed.html || '';
                
                // 处理附件信息
                if (parsed.attachments && parsed.attachments.length > 0) {
                  email.attachments = parsed.attachments.map(attachment => ({
                    filename: attachment.filename,
                    contentType: attachment.contentType,
                    size: attachment.size
                  }));
                }
                
                // 应用客户端过滤条件
                let matchesSender = true;
                let matchesSubject = true;
                let matchesDateRange = true;
                
                // 检查发件人
                if (senderFilter && email.from) {
                  matchesSender = email.from.toLowerCase().includes(senderFilter.toLowerCase());
                }
                
                // 检查主题
                if (subjectFilter && email.subject) {
                  matchesSubject = email.subject.toLowerCase().includes(subjectFilter.toLowerCase());
                }
                
                // 检查日期范围
                if (startDateFilter && email.date) {
                  const emailDate = new Date(email.date);
                  matchesDateRange = emailDate >= startDateFilter;
                }
                
                if (endDateFilter && email.date) {
                  const emailDate = new Date(email.date);
                  matchesDateRange = matchesDateRange && emailDate <= endDateFilter;
                }
                
                // 如果满足所有条件，添加到结果中
                if (matchesSender && matchesSubject && matchesDateRange) {
                  emails.push(email);
                  console.log(`邮件 ${seqno} 匹配搜索条件，已添加到结果中`);
                } else {
                  console.log(`邮件 ${seqno} 不匹配搜索条件，已跳过`);
                }
                
                processedCount++;
                console.log(`已处理 ${processedCount}/${limitedIds.length} 封邮件`);
              })
              .catch(err => {
                console.error(`解析邮件 ${seqno} 错误:`, err);
                processedCount++;
              });
          });
        });
        
        msg.once('error', (err) => {
          console.error(`处理邮件 ${seqno} 时出错:`, err);
          processedCount++;
        });
      });
      
      fetch.once('error', (err) => {
        console.error('获取邮件详情时出错:', err);
        reject(err);
      });
      
      fetch.once('end', () => {
        console.log('所有邮件处理完成');
        // 给一些时间让所有邮件处理完成
        setTimeout(() => {
          console.log(`返回 ${emails.length} 封邮件的详情`);
          resolve(emails);
        }, 1000);
      });
    } catch (error) {
      console.error('获取邮件详情时发生异常:', error);
      reject(error);
    }
  });
}

function connectToIMAP(authCode) {
    const imap = new Imap({
        user: '<EMAIL>',
        password: authCode,
        host: 'imap.qq.com',
        port: 993,
        tls: true
    });

    return new Promise((resolve, reject) => {
        imap.once('ready', () => resolve(imap));
        imap.once('error', (err) => reject(err));
        imap.connect();
    });
}



/**
 * 清理敏感信息，确保不在内存中保留凭证
 * @param {Object} obj - 包含敏感信息的对象
 */
function clearCredentials(obj) {
  if (obj && typeof obj === 'object') {
    for (const key in obj) {
      if (key.includes('password') || key.includes('auth') || key.includes('pass')) {
        obj[key] = null;
        delete obj[key];
      }
    }
  }
}

/**
 * 验证授权码格式
 * @param {string} authCode - QQ邮箱授权码
 * @returns {boolean} 是否为有效格式
 */
function validateAuthCode(authCode) {
  // QQ邮箱授权码通常是16位字符串
  return typeof authCode === 'string' && 
         authCode.length >= 14 && 
         authCode.length <= 20 &&
         /^[a-zA-Z0-9]+$/.test(authCode);
}

/**
 * 验证邮箱地址格式
 * @param {string} email - 邮箱地址
 * @returns {boolean} 是否为有效的QQ邮箱
 */
function validateQQEmail(email) {
  const qqEmailRegex = /^[a-zA-Z0-9._%+-]+@qq\.com$/;
  return qqEmailRegex.test(email);
}

/**
 * 内部测试函数
 */
async function internalTest() {
  console.log('=== 内部测试开始 ===');
  const email = '<EMAIL>';
  const authCode = 'vzbropcxoztvbhea';

  try {
    const results = await searchEmailsWithIMAP(email, authCode, '', '', '', '');
    console.log(`内部测试成功，找到 ${results.length} 封邮件`);
    return results;
  } catch (error) {
    console.error('内部测试失败:', error.message);
    throw error;
  }
}

module.exports = {
  searchEmails,
  QQ_IMAP_CONFIGS,
  tryIMAPConnection,
  internalTest
};
