/**
 * QQ邮箱搜索MCP工具 - 服务器入口
 */
const express = require('express');
const emailService = require('./email-service');
const excelService = require('./excel-service');
const path = require('path');
const fs = require('fs');

// 创建Express应用
const app = express();
const PORT = process.env.PORT || 3000;

// 中间件
app.use(express.json());

// 提供静态文件服务 - 为UI界面提供访问
app.use(express.static(path.join(__dirname, 'ui')));

// 主页路由 - 重定向到UI界面
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'ui', 'index.html'));
});

// 调试页面路由
app.get('/debug', (req, res) => {
  res.sendFile(path.join(__dirname, '..', 'debug.html'));
});

// 连接测试API
app.post('/test-connection', async (req, res) => {
  const { email, auth_code } = req.body;

  if (!email || !auth_code) {
    return res.status(400).json({ error: '邮箱地址和授权码是必需的' });
  }

  try {
    const { testAllConfigs } = require('../test-connection');
    const configIndex = await testAllConfigs(email, auth_code);

    if (configIndex >= 0) {
      res.json({
        success: true,
        message: '连接测试成功',
        configIndex: configIndex
      });
    } else {
      res.json({
        success: false,
        message: '所有配置都无法连接，请检查邮箱设置'
      });
    }
  } catch (error) {
    console.error('连接测试错误:', error);
    res.status(500).json({ error: error.message });
  }
});

// MCP协议接口
app.post('/mcp/tools', (req, res) => {
  const { tool_name, arguments: args } = req.body;
  
  console.log(`收到MCP工具请求: ${tool_name}`);
  
  switch (tool_name) {
    case 'search_qq_email':
      handleSearchEmail(args, res);
      break;
    case 'export_to_excel':
      handleExportToExcel(args, res);
      break;
    default:
      res.status(400).json({
        error: `未知工具: ${tool_name}`,
        available_tools: ['search_qq_email', 'export_to_excel']
      });
  }
});

// MCP工具描述
app.get('/mcp/describe', (req, res) => {
  res.json({
    name: "qq-email-search-mcp",
    description: "搜索QQ邮箱内容并保存到本地磁盘",
    tools: [
      {
        name: "search_qq_email",
        description: "搜索QQ邮箱内容",
        input_schema: {
          type: "object",
          properties: {
            email: { type: "string", description: "QQ邮箱地址" },
            auth_code: { type: "string", description: "QQ邮箱授权码" },
            protocol: { 
              type: "string", 
              description: "使用的协议，可选值：'imap'或'pop3'，默认为'imap'",
              enum: ["imap", "pop3"]
            },
            sender: { type: "string", description: "发件人(可选)" },
            subject: { type: "string", description: "邮件主题(可选)" },
            start_date: { type: "string", description: "开始日期 YYYY-MM-DD(可选)" },
            end_date: { type: "string", description: "结束日期 YYYY-MM-DD(可选)" }
          },
          required: ["email", "auth_code"]
        }
      },
      {
        name: "export_to_excel",
        description: "将搜索结果导出为Excel",
        input_schema: {
          type: "object",
          properties: {
            search_results: { 
              type: "array", 
              description: "搜索结果数组" 
            },
            file_path: { 
              type: "string", 
              description: "保存Excel文件的路径" 
            }
          },
          required: ["search_results", "file_path"]
        }
      }
    ],
    resources: []
  });
});

// 处理搜索邮件请求
async function handleSearchEmail(args, res) {
  try {
    const { email, auth_code, protocol, sender, subject, start_date, end_date } = args;
    
    // 验证必要参数
    if (!email || !auth_code) {
      return res.status(400).json({ error: "邮箱地址和授权码是必需的" });
    }
    
    // 验证协议参数
    const validProtocols = ['imap', 'pop3'];
    const selectedProtocol = protocol ? protocol.toLowerCase() : 'imap';
    
    if (protocol && !validProtocols.includes(selectedProtocol)) {
      return res.status(400).json({ 
        error: `无效的协议: ${protocol}，有效值为: ${validProtocols.join(', ')}` 
      });
    }
    
    // 调用邮件服务搜索邮件
    const searchResults = await emailService.searchEmails({
      email,
      auth_code,
      protocol: selectedProtocol,
      sender,
      subject,
      startDate: start_date,
      endDate: end_date
    });
    
    res.json({ results: searchResults });
  } catch (error) {
    console.error('搜索邮件错误:', error);
    res.status(500).json({ error: error.message || '搜索邮件失败' });
  }
}

// 处理导出Excel请求
async function handleExportToExcel(args, res) {
  try {
    const { search_results, file_path } = args;
    
    // 验证必要参数
    if (!search_results || !file_path) {
      return res.status(400).json({ error: "搜索结果和文件路径是必需的" });
    }
    
    // 确保目录存在
    const dir = path.dirname(file_path);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    
    // 调用Excel服务导出数据
    const excelPath = await excelService.exportToExcel(search_results, file_path);
    
    res.json({ success: true, file_path: excelPath });
  } catch (error) {
    console.error('导出Excel错误:', error);
    res.status(500).json({ error: error.message || '导出Excel失败' });
  }
}

// 启动服务器
app.listen(PORT, () => {
  console.log(`QQ邮箱搜索MCP服务器运行在 http://localhost:${PORT}`);
  console.log('可用工具: search_qq_email, export_to_excel');
});

// 处理进程退出
process.on('SIGINT', () => {
  console.log('关闭MCP服务器...');
  process.exit(0);
});
