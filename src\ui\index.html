<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>QQ邮箱搜索工具</title>
  <link rel="stylesheet" href="styles.css">
</head>
<body>
  <div class="container">
    <h1>QQ邮箱搜索工具</h1>
    
    <!-- 认证信息 -->
    <div class="section">
      <h2>邮箱认证</h2>
      
      <!-- 安全提示 -->
      <div class="security-notice">
        <h3>🔒 安全说明</h3>
        <ul>
          <li>✅ 本工具不会存储您的邮箱密码或授权码</li>
          <li>✅ 所有连接均使用 SSL/TLS 加密传输</li>
          <li>✅ 授权码仅在搜索过程中临时使用，完成后立即清除</li>
          <li>⚠️ 请勿在公共计算机上使用</li>
        </ul>
      </div>
      
      <div class="form-group">
        <label for="email">QQ邮箱地址:</label>
        <input type="email" id="email" placeholder="例如: <EMAIL>" required autocomplete="off">
        <div class="help-text">请输入完整的QQ邮箱地址</div>
      </div>
      
      <div class="form-group">
        <label for="authCode">授权码:</label>
        <input type="password" id="authCode" name="authCode" required autocomplete="off" />
        <div class="help-text">
          请输入QQ邮箱的授权码（非QQ密码）。
          <a href="#" onclick="showAuthCodeHelp()">如何获取授权码？</a>
        </div>
      </div>
      
      <div class="form-group">
        <label for="protocol">邮箱协议:</label>
        <select id="protocol">
          <option value="imap" selected>IMAP (推荐，安全性更高)</option>
          <option value="pop3">POP3</option>
        </select>
        <div class="help-text">
          IMAP使用SSL加密，可以保留服务器上的邮件；POP3会下载后删除服务器上的邮件
        </div>
      </div>
    </div>
    
    <!-- 搜索条件 -->
    <div class="section">
      <h2>搜索条件</h2>

      <!-- 搜索历史 -->
      <div class="search-history" id="search-history" style="display: none;">
        <h3>搜索历史</h3>
        <div class="history-list" id="history-list">
          <!-- 搜索历史项将在这里动态添加 -->
        </div>
        <button type="button" class="clear-history-btn" onclick="clearSearchHistory()">清除历史</button>
      </div>
      <div class="form-group">
        <label for="sender">发件人:</label>
        <input type="text" id="sender" placeholder="发件人邮箱地址(可选)">
      </div>
      <div class="form-group">
        <label for="subject">主题:</label>
        <input type="text" id="subject" placeholder="邮件主题关键词(可选)">
      </div>
      <div class="form-group date-range">
        <div class="date-input">
          <label for="start-date">开始日期:</label>
          <input type="date" id="start-date">
        </div>
        <div class="date-input">
          <label for="end-date">结束日期:</label>
          <input type="date" id="end-date">
        </div>
      </div>
    </div>
    
    <!-- 操作按钮 -->
    <div class="actions">
      <button id="test-connection-btn" class="secondary-btn" onclick="testConnection()">测试连接</button>
      <button id="search-btn" class="primary-btn">搜索邮件</button>
      <button id="smart-search-btn" class="secondary-btn" onclick="smartSearch()">智能搜索</button>
      <button id="export-btn" class="secondary-btn" disabled>导出Excel</button>
      <button id="test-btn" class="secondary-btn" onclick="testFeatures()" style="margin-left: 10px;">测试功能</button>
    </div>
    
    <!-- 搜索结果 -->
    <div class="section results-section">
      <h2>搜索结果 <span id="result-count">(0)</span></h2>
      <div class="loading-indicator" id="loading-indicator">
        <div class="spinner"></div>
        <p>正在搜索，请稍候...</p>
      </div>
      <div class="results-container">
        <table id="results-table">
          <thead>
            <tr>
              <th>发件人</th>
              <th>主题</th>
              <th>日期</th>
              <th>附件</th>
            </tr>
          </thead>
          <tbody id="results-body">
            <!-- 搜索结果将在这里动态添加 -->
          </tbody>
        </table>
      </div>
      <div id="no-results" class="no-results">
        <p>没有找到匹配的邮件。</p>
      </div>
    </div>
    
    <!-- 状态栏 -->
    <div class="status-bar">
      <span id="status-message">准备就绪</span>
      <div id="debug-info" style="font-size: 12px; color: #666; margin-top: 5px; display: none;">
        调试信息将在这里显示
      </div>
    </div>

    <!-- 通知系统 -->
    <div id="notification-container" class="notification-container">
      <!-- 通知将在这里动态添加 -->
    </div>
  </div>
  
  <!-- 授权码帮助弹窗 -->
  <div id="authCodeModal" class="modal">
    <div class="modal-content">
      <span class="close" onclick="closeAuthCodeHelp()">&times;</span>
      <h3>如何获取QQ邮箱授权码</h3>
      <div class="auth-help-content">
        <ol>
          <li>登录QQ邮箱网页版</li>
          <li>点击上方的"设置" → "账户"</li>
          <li>找到"POP3/IMAP/SMTP/Exchange/CardDAV/CalDAV服务"</li>
          <li>开启"IMAP/SMTP服务"或"POP3/SMTP服务"</li>
          <li>通过密保验证后，系统会显示授权码</li>
          <li>复制授权码并粘贴到上方输入框</li>
        </ol>
        <div class="security-reminder">
          <strong>安全提醒：</strong>
          <ul>
            <li>授权码相当于邮箱密码，请妥善保管</li>
            <li>本工具不会保存您的授权码</li>
            <li>如有疑虑，可在使用后重新生成授权码</li>
          </ul>
        </div>
      </div>
    </div>
  </div>

  <script src="renderer.js?v=9.0"></script>
</body>
</html>
