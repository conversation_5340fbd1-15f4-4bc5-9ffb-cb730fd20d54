<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快速测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        button { padding: 10px 20px; margin: 10px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .log { background: #f8f9fa; padding: 10px; margin: 10px 0; border-radius: 4px; font-family: monospace; font-size: 12px; max-height: 400px; overflow-y: auto; }
    </style>
</head>
<body>
    <h1>快速测试 - 检查原始前端问题</h1>
    
    <button onclick="testOriginalPage()">测试原始页面功能</button>
    <button onclick="testDirectAPI()">直接测试API</button>
    <button onclick="clearLog()">清除日志</button>
    
    <div id="log" class="log"></div>

    <script>
        function log(message) {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${time}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${time}] ${message}`);
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        async function testDirectAPI() {
            log('🔍 直接测试API调用...');
            
            try {
                const response = await fetch('http://localhost:3000/mcp/tools', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        tool_name: 'search_qq_email',
                        arguments: {
                            email: '<EMAIL>',
                            auth_code: 'vzbropcxoztvbhea',
                            protocol: 'imap',
                            sender: '<EMAIL>',
                            start_date: '2023-11-01',
                            end_date: '2023-11-30'
                        }
                    })
                });
                
                log(`📊 API响应状态: ${response.status}`);
                
                if (!response.ok) {
                    const errorData = await response.json();
                    log(`❌ API错误: ${errorData.error}`);
                    return;
                }
                
                const data = await response.json();
                log(`✅ API成功: ${data.results.length} 封邮件`);
                
                const emailsWithAttachments = data.results.filter(email => 
                    email.attachments && email.attachments.length > 0
                );
                log(`📎 有附件的邮件: ${emailsWithAttachments.length} 封`);
                
                if (emailsWithAttachments.length > 0) {
                    log('📎 附件详情:');
                    emailsWithAttachments.slice(0, 3).forEach((email, index) => {
                        log(`  邮件 ${index + 1}: UID=${email.uid}, ${email.attachments.length}个附件`);
                    });
                }
                
            } catch (error) {
                log(`❌ API调用失败: ${error.message}`);
            }
        }
        
        async function testOriginalPage() {
            log('🔍 测试原始页面功能...');
            
            try {
                // 尝试打开原始页面并检查元素
                const originalWindow = window.open('http://localhost:3000', '_blank');
                
                if (!originalWindow) {
                    log('❌ 无法打开原始页面窗口');
                    return;
                }
                
                log('✅ 原始页面窗口已打开');
                log('💡 请在原始页面中进行搜索，然后查看浏览器控制台');
                log('💡 如果没有看到下载按钮，请检查:');
                log('   1. 浏览器控制台是否有JavaScript错误');
                log('   2. 搜索是否成功执行');
                log('   3. 搜索结果是否包含附件信息');
                
                // 等待一段时间后检查
                setTimeout(() => {
                    try {
                        // 尝试访问原始页面的一些元素（如果同源的话）
                        log('🔍 尝试检查原始页面状态...');
                        
                        if (originalWindow.document) {
                            const searchBtn = originalWindow.document.getElementById('search-btn');
                            log(`🔘 搜索按钮: ${searchBtn ? '存在' : '不存在'}`);
                            
                            const resultsTable = originalWindow.document.getElementById('results-table');
                            log(`📋 结果表格: ${resultsTable ? '存在' : '不存在'}`);
                            
                            if (resultsTable) {
                                const rows = resultsTable.querySelectorAll('tbody tr');
                                log(`📊 结果行数: ${rows.length}`);
                            }
                        } else {
                            log('⚠️ 无法访问原始页面DOM（可能是同源策略限制）');
                        }
                    } catch (error) {
                        log(`⚠️ 检查原始页面时出错: ${error.message}`);
                    }
                }, 2000);
                
            } catch (error) {
                log(`❌ 测试原始页面失败: ${error.message}`);
            }
        }
        
        log('🚀 快速测试页面已加载');
        log('💡 点击按钮开始测试');
    </script>
</body>
</html>
