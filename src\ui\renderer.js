/**
 * QQ邮箱搜索MCP工具 - 前端脚本
 */
// 检查是否在Electron环境中
let ipc<PERSON><PERSON><PERSON> = null;
try {
  if (typeof require !== 'undefined') {
    ipcRenderer = require('electron').ipcRenderer;
  }
} catch (e) {
  // 在浏览器环境中，不需要ipcRenderer
  console.log('运行在浏览器环境中');
}

// MCP服务器URL
const MCP_SERVER_URL = 'http://localhost:3000';

// DOM元素 - 将在DOMContentLoaded后初始化
let emailInput, authCodeInput, protocolSelect, senderInput, subjectInput;
let startDateInput, endDateInput, searchBtn, exportBtn, resultsTable;
let resultsBody, resultCount, loadingIndicator, noResults, statusMessage;
let loadingMessage, progressBar, progressText, notificationContainer;

// 状态管理系统
const AppState = {
  // 搜索相关状态
  searchResults: [],
  isSearching: false,
  lastSearchParams: null,
  searchHistory: [],

  // UI状态
  currentView: 'search', // 'search', 'results'
  searchProgress: 0, // 0-100
  searchStep: '',

  // 方法
  setSearching(isSearching) {
    this.isSearching = isSearching;
    if (!isSearching) {
      this.searchProgress = 0;
      this.searchStep = '';
    }
    this.updateUI();
  },

  setSearchProgress(progress, step = '') {
    this.searchProgress = Math.max(0, Math.min(100, progress));
    this.searchStep = step;
    this.updateProgressUI();
  },

  setSearchResults(results, searchParams = null) {
    this.searchResults = results;
    if (searchParams) {
      this.lastSearchParams = { ...searchParams };
      delete this.lastSearchParams.auth_code; // 不保存敏感信息
      this.addToSearchHistory(this.lastSearchParams);
    }
    this.currentView = results.length > 0 ? 'results' : 'search';
    this.updateUI();
  },

  addToSearchHistory(params) {
    // 避免重复的搜索历史
    const existing = this.searchHistory.findIndex(item =>
      JSON.stringify(item) === JSON.stringify(params)
    );
    if (existing !== -1) {
      this.searchHistory.splice(existing, 1);
    }

    this.searchHistory.unshift(params);
    // 只保留最近10次搜索
    if (this.searchHistory.length > 10) {
      this.searchHistory = this.searchHistory.slice(0, 10);
    }

    this.saveToLocalStorage();
    this.updateSearchHistoryUI();
  },

  loadFromLocalStorage() {
    try {
      const saved = localStorage.getItem('qq-email-search-history');
      if (saved) {
        const data = JSON.parse(saved);
        this.searchHistory = data.searchHistory || [];
      }
    } catch (error) {
      console.warn('加载搜索历史失败:', error);
    }
  },

  saveToLocalStorage() {
    try {
      localStorage.setItem('qq-email-search-history', JSON.stringify({
        searchHistory: this.searchHistory
      }));
    } catch (error) {
      console.warn('保存搜索历史失败:', error);
    }
  },

  clearHistory() {
    this.searchHistory = [];
    this.saveToLocalStorage();
    this.updateSearchHistoryUI();
  },

  updateUI() {
    // 更新搜索按钮状态
    if (searchBtn) {
      searchBtn.disabled = this.isSearching;
      searchBtn.textContent = this.isSearching ? '搜索中...' : '搜索邮件';
    }

    // 更新导出按钮状态
    if (exportBtn) {
      exportBtn.disabled = this.searchResults.length === 0 || this.isSearching;
    }

    // 更新加载指示器
    if (loadingIndicator) {
      loadingIndicator.style.display = this.isSearching ? 'flex' : 'none';
    }

    // 更新结果计数
    if (resultCount) {
      resultCount.textContent = `(${this.searchResults.length})`;
    }
  },

  updateSearchHistoryUI() {
    const historyContainer = document.getElementById('search-history');
    const historyList = document.getElementById('history-list');

    if (!historyContainer || !historyList) return;

    if (this.searchHistory.length === 0) {
      historyContainer.style.display = 'none';
      return;
    }

    historyContainer.style.display = 'block';
    historyList.innerHTML = '';

    this.searchHistory.forEach((item, index) => {
      const historyItem = document.createElement('div');
      historyItem.className = 'history-item';
      historyItem.onclick = () => applySearchHistory(item);

      const summary = document.createElement('div');
      summary.className = 'history-item-summary';

      let summaryText = '';
      if (item.sender) summaryText += `发件人: ${item.sender} `;
      if (item.subject) summaryText += `主题: ${item.subject} `;
      if (!summaryText) summaryText = '全部邮件';

      summary.textContent = summaryText;

      const details = document.createElement('div');
      details.className = 'history-item-details';

      let detailsText = `协议: ${item.protocol || 'imap'}`;
      if (item.start_date || item.end_date) {
        detailsText += ` | 日期: ${item.start_date || '不限'} 至 ${item.end_date || '不限'}`;
      }

      details.textContent = detailsText;

      historyItem.appendChild(summary);
      historyItem.appendChild(details);
      historyList.appendChild(historyItem);
    });
  },

  updateProgressUI() {
    if (progressBar) {
      progressBar.style.width = `${this.searchProgress}%`;
    }
    if (progressText) {
      progressText.textContent = this.searchStep || `${this.searchProgress}%`;
    }
  }
};

// 兼容性：保持原有的searchResults变量
let searchResults = AppState.searchResults;

/**
 * 初始化DOM元素
 */
function initDOMElements() {
  emailInput = document.getElementById('email');
  authCodeInput = document.getElementById('authCode');
  protocolSelect = document.getElementById('protocol');
  senderInput = document.getElementById('sender');
  subjectInput = document.getElementById('subject');
  startDateInput = document.getElementById('start-date');
  endDateInput = document.getElementById('end-date');
  searchBtn = document.getElementById('search-btn');
  exportBtn = document.getElementById('export-btn');
  resultsTable = document.getElementById('results-table');
  resultsBody = document.getElementById('results-body');
  resultCount = document.getElementById('result-count');
  loadingIndicator = document.getElementById('loading-indicator');
  noResults = document.getElementById('no-results');
  statusMessage = document.getElementById('status-message');
  loadingMessage = document.getElementById('loading-message');
  progressBar = document.getElementById('progress-bar');
  progressText = document.getElementById('progress-text');
  notificationContainer = document.getElementById('notification-container');

  console.log('DOM元素初始化完成');
  console.log('搜索按钮:', searchBtn);
  console.log('邮箱输入:', emailInput);
  console.log('授权码输入:', authCodeInput);

  // 显示调试信息
  const debugInfo = document.getElementById('debug-info');
  if (debugInfo) {
    debugInfo.style.display = 'block';
    debugInfo.innerHTML = `
      DOM元素状态:
      搜索按钮: ${searchBtn ? '✓' : '✗'} |
      邮箱输入: ${emailInput ? '✓' : '✗'} |
      授权码输入: ${authCodeInput ? '✓' : '✗'} |
      状态管理: ${typeof AppState !== 'undefined' ? '✓' : '✗'}
    `;
  }
}

// 初始化
document.addEventListener('DOMContentLoaded', () => {
  console.log('DOMContentLoaded 事件触发');

  // 首先初始化DOM元素
  initDOMElements();

  // 加载保存的搜索历史
  AppState.loadFromLocalStorage();

  // 初始化UI状态
  AppState.updateUI();

  // 隐藏加载指示器和无结果提示
  if (loadingIndicator) loadingIndicator.style.display = 'none';
  if (noResults) noResults.style.display = 'none';

  // 设置今天的日期为结束日期的默认值
  const today = new Date().toISOString().split('T')[0];
  if (endDateInput) endDateInput.value = today;

  // 设置30天前的日期为开始日期的默认值
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
  if (startDateInput) startDateInput.value = thirtyDaysAgo.toISOString().split('T')[0];

  // 初始化搜索历史UI
  initSearchHistoryUI();

  // 添加实时输入验证
  setupInputValidation();

  // 设置搜索按钮事件
  setupSearchButton();

  // 设置导出按钮事件
  setupExportButton();

  console.log('初始化完成');
});

/**
 * 设置搜索按钮事件
 */
function setupSearchButton() {
  if (!searchBtn) {
    console.error('搜索按钮未找到');
    return;
  }

  console.log('设置搜索按钮事件监听器');
  searchBtn.addEventListener('click', async () => {
    console.log('搜索按钮被点击');
  const email = emailInput.value.trim();
  const authCode = authCodeInput.value.trim();

  // 验证输入
  const validationErrors = validateInputs(email, authCode);
  if (validationErrors.length > 0) {
    showError(validationErrors.join('; '));
    // 高亮显示有问题的输入框
    highlightInvalidInputs(email, authCode);
    return;
  }

  // 清除之前的高亮
  clearInputHighlights();

  try {
    // 设置搜索状态
    AppState.setSearching(true);
    AppState.setSearchProgress(10, '正在验证输入参数...');

    // 隐藏之前的结果
    if (noResults) noResults.style.display = 'none';
    if (resultsTable) resultsTable.style.display = 'none';
    if (resultsBody) resultsBody.innerHTML = '';

    showInfo('正在验证邮箱凭证...');

    const searchParams = {
      email: email,
      auth_code: authCode,
      protocol: protocolSelect.value,
      sender: senderInput.value.trim() || undefined,
      subject: subjectInput.value.trim() || undefined,
      start_date: startDateInput.value || undefined,
      end_date: endDateInput.value || undefined
    };

    // 验证日期格式和逻辑
    AppState.setSearchProgress(20, '验证日期格式...');

    if (searchParams.start_date && isNaN(new Date(searchParams.start_date).getTime())) {
      throw new Error('开始日期格式无效');
    }

    if (searchParams.end_date && isNaN(new Date(searchParams.end_date).getTime())) {
      throw new Error('结束日期格式无效');
    }

    if (searchParams.start_date && searchParams.end_date) {
      const startDate = new Date(searchParams.start_date);
      const endDate = new Date(searchParams.end_date);
      if (startDate > endDate) {
        throw new Error('开始日期不能晚于结束日期');
      }
    }

    AppState.setSearchProgress(30, `连接到QQ邮箱 (${protocolSelect.value.toUpperCase()})...`);
    showInfo(`正在连接到QQ邮箱 (${protocolSelect.value.toUpperCase()})...`);

    // 调用MCP工具搜索邮件
    AppState.setSearchProgress(50, '正在搜索邮件...');

    const response = await fetch(`${MCP_SERVER_URL}/mcp/tools`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        tool_name: 'search_qq_email',
        arguments: searchParams
      })
    });

    AppState.setSearchProgress(70, '处理搜索响应...');

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `搜索失败: ${response.statusText}`);
    }

    const data = await response.json();
    const results = data.results || [];

    AppState.setSearchProgress(90, '整理搜索结果...');

    // 立即清理敏感数据
    clearSensitiveData();

    // 使用状态管理更新结果
    AppState.setSearchProgress(100, '搜索完成');
    AppState.setSearchResults(results, searchParams);

    // 更新结果显示
    displaySearchResults(results);

    if (results.length === 0) {
      showInfo('未找到匹配的邮件');

      // 提供智能建议
      const suggestions = [];
      if (senderInput && senderInput.value.trim()) {
        suggestions.push('尝试简化发件人条件（如只输入邮箱地址部分）');
      }
      if (startDateInput && startDateInput.value) {
        suggestions.push('尝试放宽日期范围');
      }
      if (subjectInput && subjectInput.value.trim()) {
        suggestions.push('尝试简化主题关键词');
      }
      if (suggestions.length === 0) {
        suggestions.push('尝试不设置任何条件进行全部搜索');
      }

      NotificationSystem.warning(
        '搜索建议：<br>• ' + suggestions.join('<br>• '),
        '未找到匹配邮件'
      );
    } else {
      showSuccess(`搜索完成，找到 ${results.length} 封邮件`);
      NotificationSystem.success(`成功找到 ${results.length} 封邮件`, '搜索完成');
    }

  } catch (error) {
    console.error('搜索错误:', error);

    // 确保在错误时也清理敏感数据
    clearSensitiveData();

    // 重置搜索状态
    AppState.setSearching(false);

    // 根据错误类型提供更具体的错误消息
    let errorMessage = error.message;
    const protocol = protocolSelect.value.toUpperCase();

    if (errorMessage.includes('连接失败') || errorMessage.includes('Invalid credentials') ||
        errorMessage.includes('登录失败')) {
      errorMessage = '连接失败：邮箱地址或授权码不正确';
    } else if (errorMessage.includes('Connection timed out') || errorMessage.includes('ECONNREFUSED')) {
      errorMessage = `连接超时：请检查网络连接和${protocol}服务是否已开启`;
    } else if (errorMessage.includes('日期')) {
      errorMessage = `日期格式错误：${errorMessage}`;
    }

    showError(`搜索失败: ${errorMessage}`);
    if (noResults) noResults.style.display = 'block';

  } finally {
    // 确保搜索状态被重置
    AppState.setSearching(false);
  }
  });
}

/**
 * 设置导出按钮事件
 */
function setupExportButton() {
  if (!exportBtn) {
    console.error('导出按钮未找到');
    return;
  }

  console.log('设置导出按钮事件监听器');
  exportBtn.addEventListener('click', async () => {
  if (AppState.searchResults.length === 0) {
    showError('没有可导出的搜索结果');
    return;
  }
  
  try {
    // 选择保存路径
    let filePath;
    if (ipcRenderer) {
      // Electron环境
      filePath = await ipcRenderer.invoke('select-save-path');
      if (!filePath) {
        return;
      }
    } else {
      // 浏览器环境，使用默认路径
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      filePath = `qq-email-search-${timestamp}.xlsx`;
    }
    
    statusMessage.textContent = '正在导出Excel...';
    exportBtn.disabled = true;
    
    // 调用MCP工具导出Excel
    const response = await fetch(`${MCP_SERVER_URL}/mcp/tools`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        tool_name: 'export_to_excel',
        arguments: {
          search_results: AppState.searchResults,
          file_path: filePath
        }
      })
    });
    
    if (!response.ok) {
      throw new Error(`导出失败: ${response.statusText}`);
    }
    
    const data = await response.json();
    
    if (data.success) {
      await showSuccess(`Excel文件已成功导出到: ${data.file_path}`);
      statusMessage.textContent = '导出完成';
    } else {
      throw new Error('导出失败');
    }
  } catch (error) {
    console.error('导出错误:', error);
    await showError(`导出失败: ${error.message}`);
    statusMessage.textContent = '导出失败';
  } finally {
    exportBtn.disabled = false;
  }
  });
}

// 显示搜索结果
function displaySearchResults(results) {
  resultsBody.innerHTML = '';
  
  if (results.length === 0) {
    noResults.style.display = 'block';
    exportBtn.disabled = true;
    resultCount.textContent = '(0)';
    return;
  }
  
  noResults.style.display = 'none';
  exportBtn.disabled = false;
  resultCount.textContent = `(${results.length})`;
  
  results.forEach(email => {
    const row = document.createElement('tr');
    
    // 发件人
    const fromCell = document.createElement('td');
    fromCell.textContent = email.from;
    row.appendChild(fromCell);
    
    // 主题
    const subjectCell = document.createElement('td');
    subjectCell.textContent = email.subject;
    row.appendChild(subjectCell);
    
    // 日期
    const dateCell = document.createElement('td');
    dateCell.textContent = formatDate(email.date);
    row.appendChild(dateCell);
    
    // 附件
    const attachmentsCell = document.createElement('td');
    if (email.attachments && email.attachments.length > 0) {
      attachmentsCell.textContent = `${email.attachments.length}个附件`;
    } else {
      attachmentsCell.textContent = '无';
    }
    row.appendChild(attachmentsCell);
    
    resultsBody.appendChild(row);
  });
}

// 格式化日期
function formatDate(dateString) {
  if (!dateString) return '';
  
  try {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch (error) {
    console.error('日期格式化错误:', error);
    return dateString;
  }
}

/**
 * 安全清理函数
 */
function clearSensitiveData() {
  // 清理授权码输入框
  const authCodeInput = document.getElementById('authCode');
  if (authCodeInput) {
    authCodeInput.value = '';
  }
  
  // 清理内存中可能存在的敏感数据
  if (window.lastSearchParams) {
    delete window.lastSearchParams.auth_code;
    delete window.lastSearchParams.password;
  }
}

/**
 * 显示授权码帮助弹窗
 */
function showAuthCodeHelp() {
  const modal = document.getElementById('authCodeModal');
  if (modal) {
    modal.style.display = 'block';
  }
}

/**
 * 关闭授权码帮助弹窗
 */
function closeAuthCodeHelp() {
  const modal = document.getElementById('authCodeModal');
  if (modal) {
    modal.style.display = 'none';
  }
}

/**
 * 验证输入数据
 */
function validateInputs(email, authCode) {
  const errors = [];

  // 验证邮箱格式
  const emailRegex = /^[a-zA-Z0-9._%+-]+@qq\.com$/;
  if (!emailRegex.test(email)) {
    errors.push('请输入有效的QQ邮箱地址（如：<EMAIL>）');
  }

  // 验证授权码格式
  if (!authCode || authCode.length < 14 || authCode.length > 20) {
    errors.push('授权码格式不正确，请确认是QQ邮箱的授权码');
  }

  if (!/^[a-zA-Z0-9]+$/.test(authCode)) {
    errors.push('授权码只能包含字母和数字');
  }

  return errors;
}

/**
 * 高亮显示无效输入
 */
function highlightInvalidInputs(email, authCode) {
  // 验证邮箱
  const emailRegex = /^[a-zA-Z0-9._%+-]+@qq\.com$/;
  if (emailInput && !emailRegex.test(email)) {
    emailInput.style.borderColor = '#dc3545';
    emailInput.style.boxShadow = '0 0 0 0.2rem rgba(220, 53, 69, 0.25)';
  }

  // 验证授权码
  if (authCodeInput && (!authCode || authCode.length < 14 || authCode.length > 20 || !/^[a-zA-Z0-9]+$/.test(authCode))) {
    authCodeInput.style.borderColor = '#dc3545';
    authCodeInput.style.boxShadow = '0 0 0 0.2rem rgba(220, 53, 69, 0.25)';
  }
}

/**
 * 清除输入高亮
 */
function clearInputHighlights() {
  [emailInput, authCodeInput].forEach(input => {
    if (input) {
      input.style.borderColor = '';
      input.style.boxShadow = '';
    }
  });
}

/**
 * 设置实时输入验证
 */
function setupInputValidation() {
  // 邮箱输入验证
  if (emailInput) {
    emailInput.addEventListener('blur', () => {
      const email = emailInput.value.trim();
      const emailRegex = /^[a-zA-Z0-9._%+-]+@qq\.com$/;

      if (email && !emailRegex.test(email)) {
        emailInput.style.borderColor = '#dc3545';
        emailInput.style.boxShadow = '0 0 0 0.2rem rgba(220, 53, 69, 0.25)';
      } else {
        emailInput.style.borderColor = email ? '#28a745' : '';
        emailInput.style.boxShadow = email ? '0 0 0 0.2rem rgba(40, 167, 69, 0.25)' : '';
      }
    });

    emailInput.addEventListener('input', () => {
      if (emailInput.style.borderColor === 'rgb(220, 53, 69)') {
        emailInput.style.borderColor = '';
        emailInput.style.boxShadow = '';
      }
    });
  }

  // 授权码输入验证
  if (authCodeInput) {
    authCodeInput.addEventListener('blur', () => {
      const authCode = authCodeInput.value.trim();

      if (authCode && (authCode.length < 14 || authCode.length > 20 || !/^[a-zA-Z0-9]+$/.test(authCode))) {
        authCodeInput.style.borderColor = '#dc3545';
        authCodeInput.style.boxShadow = '0 0 0 0.2rem rgba(220, 53, 69, 0.25)';
      } else {
        authCodeInput.style.borderColor = authCode ? '#28a745' : '';
        authCodeInput.style.boxShadow = authCode ? '0 0 0 0.2rem rgba(40, 167, 69, 0.25)' : '';
      }
    });

    authCodeInput.addEventListener('input', () => {
      if (authCodeInput.style.borderColor === 'rgb(220, 53, 69)') {
        authCodeInput.style.borderColor = '';
        authCodeInput.style.boxShadow = '';
      }
    });
  }
}

/**
 * 通知系统
 */
const NotificationSystem = {
  show(message, type = 'info', title = '', duration = 5000) {
    if (!notificationContainer) return;

    const notification = document.createElement('div');
    notification.className = `notification ${type}`;

    const closeBtn = document.createElement('button');
    closeBtn.className = 'notification-close';
    closeBtn.innerHTML = '×';
    closeBtn.onclick = () => this.remove(notification);

    let content = '';
    if (title) {
      content += `<div class="notification-title">${title}</div>`;
    }
    content += `<div class="notification-message">${message}</div>`;

    notification.innerHTML = content;
    notification.appendChild(closeBtn);

    // 点击通知也可以关闭
    notification.onclick = () => this.remove(notification);

    notificationContainer.appendChild(notification);

    // 自动移除
    if (duration > 0) {
      setTimeout(() => this.remove(notification), duration);
    }

    return notification;
  },

  remove(notification) {
    if (notification && notification.parentNode) {
      notification.style.animation = 'slideOut 0.3s ease-out';
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 300);
    }
  },

  success(message, title = '成功') {
    return this.show(message, 'success', title);
  },

  error(message, title = '错误') {
    return this.show(message, 'error', title, 8000); // 错误消息显示更久
  },

  info(message, title = '信息') {
    return this.show(message, 'info', title);
  },

  warning(message, title = '警告') {
    return this.show(message, 'warning', title);
  }
};

/**
 * 显示错误消息
 */
function showError(message) {
  const statusElement = document.getElementById('status-message');
  if (statusElement) {
    statusElement.textContent = `❌ ${message}`;
    statusElement.style.color = '#d32f2f';
  }

  // 使用新的通知系统
  NotificationSystem.error(message);
}

/**
 * 显示成功消息
 */
function showSuccess(message) {
  const statusElement = document.getElementById('status-message');
  if (statusElement) {
    statusElement.textContent = `✅ ${message}`;
    statusElement.style.color = '#388e3c';
  }

  // 使用新的通知系统
  NotificationSystem.success(message);
}

/**
 * 显示信息消息
 */
function showInfo(message) {
  const statusElement = document.getElementById('status-message');
  if (statusElement) {
    statusElement.textContent = message;
    statusElement.style.color = '#1976d2';
  }

  // 对于重要信息也显示通知
  if (message.includes('连接') || message.includes('搜索') || message.includes('验证')) {
    NotificationSystem.info(message);
  }
}

// 页面卸载时清理敏感数据
window.addEventListener('beforeunload', () => {
  clearSensitiveData();
});

// 页面隐藏时清理敏感数据
document.addEventListener('visibilitychange', () => {
  if (document.hidden) {
    clearSensitiveData();
  }
});

// 点击弹窗外部关闭弹窗
window.addEventListener('click', (event) => {
  const modal = document.getElementById('authCodeModal');
  if (event.target === modal) {
    closeAuthCodeHelp();
  }
});

/**
 * 初始化搜索历史UI
 */
function initSearchHistoryUI() {
  AppState.updateSearchHistoryUI();
  console.log('搜索历史功能已初始化');
}

/**
 * 清除搜索历史
 */
function clearSearchHistory() {
  if (confirm('确定要清除所有搜索历史吗？')) {
    AppState.clearHistory();
  }
}

/**
 * 应用搜索历史
 */
function applySearchHistory(historyItem) {
  if (emailInput && historyItem.email) emailInput.value = historyItem.email;
  if (protocolSelect && historyItem.protocol) protocolSelect.value = historyItem.protocol;
  if (senderInput && historyItem.sender) senderInput.value = historyItem.sender;
  if (subjectInput && historyItem.subject) subjectInput.value = historyItem.subject;
  if (startDateInput && historyItem.start_date) startDateInput.value = historyItem.start_date;
  if (endDateInput && historyItem.end_date) endDateInput.value = historyItem.end_date;
}

/**
 * 智能搜索 - 当组合条件无结果时，尝试单独条件
 */
async function smartSearch() {
  const email = emailInput?.value?.trim();
  const authCode = authCodeInput?.value?.trim();

  if (!email || !authCode) {
    showError('请先输入邮箱地址和授权码');
    return;
  }

  // 验证输入
  const validationErrors = validateInputs(email, authCode);
  if (validationErrors.length > 0) {
    showError(validationErrors.join('; '));
    return;
  }

  try {
    AppState.setSearching(true);
    AppState.setSearchProgress(10, '开始智能搜索...');

    showInfo('智能搜索：尝试多种搜索策略...');

    const sender = senderInput?.value?.trim();
    const subject = subjectInput?.value?.trim();
    const startDate = startDateInput?.value;
    const endDate = endDateInput?.value;

    // 策略1：完整条件搜索
    AppState.setSearchProgress(20, '尝试完整条件搜索...');
    let results = await performSearch(email, authCode, sender, subject, startDate, endDate);

    if (results.length > 0) {
      AppState.setSearchResults(results, { email, sender, subject, start_date: startDate, end_date: endDate });
      displaySearchResults(results);
      showSuccess(`智能搜索成功：找到 ${results.length} 封邮件`);
      return;
    }

    // 策略2：只按发件人搜索
    if (sender) {
      AppState.setSearchProgress(40, '尝试发件人搜索...');
      results = await performSearch(email, authCode, sender, '', '', '');
      if (results.length > 0) {
        AppState.setSearchResults(results, { email, sender });
        displaySearchResults(results);
        showSuccess(`智能搜索成功：按发件人找到 ${results.length} 封邮件`);
        NotificationSystem.info('已移除日期和主题限制', '搜索策略调整');
        return;
      }
    }

    // 策略3：只按主题搜索
    if (subject) {
      AppState.setSearchProgress(60, '尝试主题搜索...');
      results = await performSearch(email, authCode, '', subject, '', '');
      if (results.length > 0) {
        AppState.setSearchResults(results, { email, subject });
        displaySearchResults(results);
        showSuccess(`智能搜索成功：按主题找到 ${results.length} 封邮件`);
        NotificationSystem.info('已移除发件人和日期限制', '搜索策略调整');
        return;
      }
    }

    // 策略4：只按日期搜索
    if (startDate || endDate) {
      AppState.setSearchProgress(80, '尝试日期范围搜索...');
      results = await performSearch(email, authCode, '', '', startDate, endDate);
      if (results.length > 0) {
        AppState.setSearchResults(results, { email, start_date: startDate, end_date: endDate });
        displaySearchResults(results);
        showSuccess(`智能搜索成功：按日期找到 ${results.length} 封邮件`);
        NotificationSystem.info('已移除发件人和主题限制', '搜索策略调整');
        return;
      }
    }

    // 策略5：无条件搜索（最近邮件）
    AppState.setSearchProgress(90, '获取最近邮件...');
    results = await performSearch(email, authCode, '', '', '', '');
    if (results.length > 0) {
      AppState.setSearchResults(results, { email });
      displaySearchResults(results);
      showSuccess(`智能搜索完成：获取最近 ${results.length} 封邮件`);
      NotificationSystem.warning('已移除所有搜索条件，显示最近邮件', '搜索策略调整');
    } else {
      showError('智能搜索失败：邮箱中没有找到任何邮件');
    }

  } catch (error) {
    console.error('智能搜索错误:', error);
    showError(`智能搜索失败：${error.message}`);
  } finally {
    AppState.setSearching(false);
  }
}

/**
 * 执行搜索的辅助函数
 */
async function performSearch(email, authCode, sender, subject, startDate, endDate) {
  const response = await fetch(`${MCP_SERVER_URL}/mcp/tools`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      tool_name: 'search_qq_email',
      arguments: {
        email: email,
        auth_code: authCode,
        protocol: protocolSelect?.value || 'imap',
        sender: sender || undefined,
        subject: subject || undefined,
        start_date: startDate || undefined,
        end_date: endDate || undefined
      }
    })
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.error || `搜索失败: ${response.statusText}`);
  }

  const data = await response.json();
  return data.results || [];
}

/**
 * 测试连接
 */
async function testConnection() {
  const email = emailInput?.value?.trim();
  const authCode = authCodeInput?.value?.trim();

  if (!email || !authCode) {
    showError('请先输入邮箱地址和授权码');
    return;
  }

  // 验证输入格式
  const validationErrors = validateInputs(email, authCode);
  if (validationErrors.length > 0) {
    showError(validationErrors.join('; '));
    return;
  }

  try {
    showInfo('正在测试连接，请稍候...');

    const response = await fetch(`${MCP_SERVER_URL}/test-connection`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: email,
        auth_code: authCode
      })
    });

    const data = await response.json();

    if (data.success) {
      showSuccess(`连接测试成功！${data.message}`);
      NotificationSystem.success('邮箱连接正常，可以开始搜索邮件', '连接测试成功');
    } else {
      showError(`连接测试失败：${data.message}`);
      NotificationSystem.error('请检查邮箱设置和网络连接', '连接测试失败');
    }
  } catch (error) {
    console.error('连接测试错误:', error);
    showError(`连接测试失败：${error.message}`);
  }
}

/**
 * 测试功能
 */
function testFeatures() {
  console.log('开始测试功能...');

  // 测试状态管理
  if (typeof AppState !== 'undefined') {
    console.log('✓ AppState存在');

    // 测试通知系统
    if (typeof NotificationSystem !== 'undefined') {
      NotificationSystem.info('功能测试：状态管理和通知系统正常工作！');
      console.log('✓ 通知系统正常');
    } else {
      console.log('✗ 通知系统不存在');
    }

    // 测试状态更新
    AppState.setSearchProgress(50, '测试进度显示');
    setTimeout(() => {
      AppState.setSearchProgress(100, '测试完成');
      setTimeout(() => {
        AppState.setSearchProgress(0, '');
      }, 1000);
    }, 1000);

    console.log('✓ 状态管理测试完成');
  } else {
    console.log('✗ AppState不存在');
  }

  // 测试DOM元素
  const elements = [emailInput, authCodeInput, searchBtn, exportBtn];
  const elementNames = ['邮箱输入', '授权码输入', '搜索按钮', '导出按钮'];

  elements.forEach((element, index) => {
    if (element) {
      console.log(`✓ ${elementNames[index]}存在`);
    } else {
      console.log(`✗ ${elementNames[index]}不存在`);
    }
  });

  showSuccess('功能测试完成，请查看控制台输出');
}

// 暴露函数到全局作用域供HTML调用
window.showAuthCodeHelp = showAuthCodeHelp;
window.closeAuthCodeHelp = closeAuthCodeHelp;
window.applySearchHistory = applySearchHistory;
window.clearSearchHistory = clearSearchHistory;
window.testConnection = testConnection;
window.smartSearch = smartSearch;
window.testFeatures = testFeatures;
