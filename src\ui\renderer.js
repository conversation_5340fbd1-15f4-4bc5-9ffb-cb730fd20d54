/**
 * QQ邮箱搜索MCP工具 - 渲染进程脚本
 */
const { ipc<PERSON><PERSON><PERSON> } = require('electron');

// MCP服务器URL
const MCP_SERVER_URL = 'http://localhost:3000';

// DOM元素
const emailInput = document.getElementById('email');
const authCodeInput = document.getElementById('authCode');
const protocolSelect = document.getElementById('protocol');
const senderInput = document.getElementById('sender');
const subjectInput = document.getElementById('subject');
const startDateInput = document.getElementById('start-date');
const endDateInput = document.getElementById('end-date');
const searchBtn = document.getElementById('search-btn');
const exportBtn = document.getElementById('export-btn');
const resultsTable = document.getElementById('results-table');
const resultsBody = document.getElementById('results-body');
const resultCount = document.getElementById('result-count');
const loadingIndicator = document.getElementById('loading-indicator');
const noResults = document.getElementById('no-results');
const statusMessage = document.getElementById('status-message');
const loadingMessage = document.getElementById('loading-message');
const progressBar = document.getElementById('progress-bar');
const progressText = document.getElementById('progress-text');

// 状态管理系统
const AppState = {
  // 搜索相关状态
  searchResults: [],
  isSearching: false,
  lastSearchParams: null,
  searchHistory: [],

  // UI状态
  currentView: 'search', // 'search', 'results'
  searchProgress: 0, // 0-100
  searchStep: '',

  // 方法
  setSearching(isSearching) {
    this.isSearching = isSearching;
    if (!isSearching) {
      this.searchProgress = 0;
      this.searchStep = '';
    }
    this.updateUI();
  },

  setSearchProgress(progress, step = '') {
    this.searchProgress = Math.max(0, Math.min(100, progress));
    this.searchStep = step;
    this.updateProgressUI();
  },

  setSearchResults(results, searchParams = null) {
    this.searchResults = results;
    if (searchParams) {
      this.lastSearchParams = { ...searchParams };
      delete this.lastSearchParams.auth_code; // 不保存敏感信息
      this.addToSearchHistory(this.lastSearchParams);
    }
    this.currentView = results.length > 0 ? 'results' : 'search';
    this.updateUI();
  },

  addToSearchHistory(params) {
    // 避免重复的搜索历史
    const existing = this.searchHistory.findIndex(item =>
      JSON.stringify(item) === JSON.stringify(params)
    );
    if (existing !== -1) {
      this.searchHistory.splice(existing, 1);
    }

    this.searchHistory.unshift(params);
    // 只保留最近10次搜索
    if (this.searchHistory.length > 10) {
      this.searchHistory = this.searchHistory.slice(0, 10);
    }

    this.saveToLocalStorage();
    this.updateSearchHistoryUI();
  },

  loadFromLocalStorage() {
    try {
      const saved = localStorage.getItem('qq-email-search-history');
      if (saved) {
        const data = JSON.parse(saved);
        this.searchHistory = data.searchHistory || [];
      }
    } catch (error) {
      console.warn('加载搜索历史失败:', error);
    }
  },

  saveToLocalStorage() {
    try {
      localStorage.setItem('qq-email-search-history', JSON.stringify({
        searchHistory: this.searchHistory
      }));
    } catch (error) {
      console.warn('保存搜索历史失败:', error);
    }
  },

  clearHistory() {
    this.searchHistory = [];
    this.saveToLocalStorage();
    this.updateSearchHistoryUI();
  },

  updateUI() {
    // 更新搜索按钮状态
    if (searchBtn) {
      searchBtn.disabled = this.isSearching;
      searchBtn.textContent = this.isSearching ? '搜索中...' : '搜索邮件';
    }

    // 更新导出按钮状态
    if (exportBtn) {
      exportBtn.disabled = this.searchResults.length === 0 || this.isSearching;
    }

    // 更新加载指示器
    if (loadingIndicator) {
      loadingIndicator.style.display = this.isSearching ? 'flex' : 'none';
    }

    // 更新结果计数
    if (resultCount) {
      resultCount.textContent = `(${this.searchResults.length})`;
    }
  },

  updateSearchHistoryUI() {
    const historyContainer = document.getElementById('search-history');
    const historyList = document.getElementById('history-list');

    if (!historyContainer || !historyList) return;

    if (this.searchHistory.length === 0) {
      historyContainer.style.display = 'none';
      return;
    }

    historyContainer.style.display = 'block';
    historyList.innerHTML = '';

    this.searchHistory.forEach((item, index) => {
      const historyItem = document.createElement('div');
      historyItem.className = 'history-item';
      historyItem.onclick = () => applySearchHistory(item);

      const summary = document.createElement('div');
      summary.className = 'history-item-summary';

      let summaryText = '';
      if (item.sender) summaryText += `发件人: ${item.sender} `;
      if (item.subject) summaryText += `主题: ${item.subject} `;
      if (!summaryText) summaryText = '全部邮件';

      summary.textContent = summaryText;

      const details = document.createElement('div');
      details.className = 'history-item-details';

      let detailsText = `协议: ${item.protocol || 'imap'}`;
      if (item.start_date || item.end_date) {
        detailsText += ` | 日期: ${item.start_date || '不限'} 至 ${item.end_date || '不限'}`;
      }

      details.textContent = detailsText;

      historyItem.appendChild(summary);
      historyItem.appendChild(details);
      historyList.appendChild(historyItem);
    });
  },

  updateProgressUI() {
    if (progressBar) {
      progressBar.style.width = `${this.searchProgress}%`;
    }
    if (progressText) {
      progressText.textContent = this.searchStep || `${this.searchProgress}%`;
    }
  }
};

// 兼容性：保持原有的searchResults变量
let searchResults = AppState.searchResults;

// 初始化
document.addEventListener('DOMContentLoaded', () => {
  // 加载保存的搜索历史
  AppState.loadFromLocalStorage();

  // 初始化UI状态
  AppState.updateUI();

  // 隐藏加载指示器和无结果提示
  if (loadingIndicator) loadingIndicator.style.display = 'none';
  if (noResults) noResults.style.display = 'none';

  // 设置今天的日期为结束日期的默认值
  const today = new Date().toISOString().split('T')[0];
  if (endDateInput) endDateInput.value = today;

  // 设置30天前的日期为开始日期的默认值
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
  if (startDateInput) startDateInput.value = thirtyDaysAgo.toISOString().split('T')[0];

  // 初始化搜索历史UI
  initSearchHistoryUI();
});

// 移除这个不存在的表单监听器，搜索功能由下面的按钮点击事件处理

// 搜索按钮点击事件
searchBtn.addEventListener('click', async () => {
  const email = emailInput.value.trim();
  const authCode = authCodeInput.value.trim();

  // 验证输入
  const validationErrors = validateInputs(email, authCode);
  if (validationErrors.length > 0) {
    showError(validationErrors.join('; '));
    return;
  }

  try {
    // 设置搜索状态
    AppState.setSearching(true);
    AppState.setSearchProgress(10, '正在验证输入参数...');

    // 隐藏之前的结果
    if (noResults) noResults.style.display = 'none';
    if (resultsTable) resultsTable.style.display = 'none';
    if (resultsBody) resultsBody.innerHTML = '';

    showInfo('正在验证邮箱凭证...');

    const searchParams = {
      email: email,
      auth_code: authCode,
      protocol: protocolSelect.value,
      sender: senderInput.value.trim() || undefined,
      subject: subjectInput.value.trim() || undefined,
      start_date: startDateInput.value || undefined,
      end_date: endDateInput.value || undefined
    };

    // 验证日期格式和逻辑
    AppState.setSearchProgress(20, '验证日期格式...');

    if (searchParams.start_date && isNaN(new Date(searchParams.start_date).getTime())) {
      throw new Error('开始日期格式无效');
    }

    if (searchParams.end_date && isNaN(new Date(searchParams.end_date).getTime())) {
      throw new Error('结束日期格式无效');
    }

    if (searchParams.start_date && searchParams.end_date) {
      const startDate = new Date(searchParams.start_date);
      const endDate = new Date(searchParams.end_date);
      if (startDate > endDate) {
        throw new Error('开始日期不能晚于结束日期');
      }
    }

    AppState.setSearchProgress(30, `连接到QQ邮箱 (${protocolSelect.value.toUpperCase()})...`);
    showInfo(`正在连接到QQ邮箱 (${protocolSelect.value.toUpperCase()})...`);

    // 调用MCP工具搜索邮件
    AppState.setSearchProgress(50, '正在搜索邮件...');

    const response = await fetch(`${MCP_SERVER_URL}/mcp/tools`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        tool_name: 'search_qq_email',
        arguments: searchParams
      })
    });

    AppState.setSearchProgress(70, '处理搜索响应...');

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `搜索失败: ${response.statusText}`);
    }

    const data = await response.json();
    const results = data.results || [];

    AppState.setSearchProgress(90, '整理搜索结果...');

    // 立即清理敏感数据
    clearSensitiveData();

    // 使用状态管理更新结果
    AppState.setSearchProgress(100, '搜索完成');
    AppState.setSearchResults(results, searchParams);

    // 更新结果显示
    displaySearchResults(results);

    if (results.length === 0) {
      showInfo('未找到匹配的邮件，请尝试调整搜索条件');
    } else {
      showSuccess(`搜索完成，找到 ${results.length} 封邮件`);
    }

  } catch (error) {
    console.error('搜索错误:', error);

    // 确保在错误时也清理敏感数据
    clearSensitiveData();

    // 重置搜索状态
    AppState.setSearching(false);

    // 根据错误类型提供更具体的错误消息
    let errorMessage = error.message;
    const protocol = protocolSelect.value.toUpperCase();

    if (errorMessage.includes('连接失败') || errorMessage.includes('Invalid credentials') ||
        errorMessage.includes('登录失败')) {
      errorMessage = '连接失败：邮箱地址或授权码不正确';
    } else if (errorMessage.includes('Connection timed out') || errorMessage.includes('ECONNREFUSED')) {
      errorMessage = `连接超时：请检查网络连接和${protocol}服务是否已开启`;
    } else if (errorMessage.includes('日期')) {
      errorMessage = `日期格式错误：${errorMessage}`;
    }

    showError(`搜索失败: ${errorMessage}`);
    if (noResults) noResults.style.display = 'block';

  } finally {
    // 确保搜索状态被重置
    AppState.setSearching(false);
  }
});

// 导出按钮点击事件
exportBtn.addEventListener('click', async () => {
  if (AppState.searchResults.length === 0) {
    showError('没有可导出的搜索结果');
    return;
  }
  
  try {
    // 选择保存路径
    const filePath = await ipcRenderer.invoke('select-save-path');
    if (!filePath) {
      // 用户取消了保存对话框
      return;
    }
    
    statusMessage.textContent = '正在导出Excel...';
    exportBtn.disabled = true;
    
    // 调用MCP工具导出Excel
    const response = await fetch(`${MCP_SERVER_URL}/mcp/tools`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        tool_name: 'export_to_excel',
        arguments: {
          search_results: AppState.searchResults,
          file_path: filePath
        }
      })
    });
    
    if (!response.ok) {
      throw new Error(`导出失败: ${response.statusText}`);
    }
    
    const data = await response.json();
    
    if (data.success) {
      await showSuccess(`Excel文件已成功导出到: ${data.file_path}`);
      statusMessage.textContent = '导出完成';
    } else {
      throw new Error('导出失败');
    }
  } catch (error) {
    console.error('导出错误:', error);
    await showError(`导出失败: ${error.message}`);
    statusMessage.textContent = '导出失败';
  } finally {
    exportBtn.disabled = false;
  }
});

// 显示搜索结果
function displaySearchResults(results) {
  resultsBody.innerHTML = '';
  
  if (results.length === 0) {
    noResults.style.display = 'block';
    exportBtn.disabled = true;
    resultCount.textContent = '(0)';
    return;
  }
  
  noResults.style.display = 'none';
  exportBtn.disabled = false;
  resultCount.textContent = `(${results.length})`;
  
  results.forEach(email => {
    const row = document.createElement('tr');
    
    // 发件人
    const fromCell = document.createElement('td');
    fromCell.textContent = email.from;
    row.appendChild(fromCell);
    
    // 主题
    const subjectCell = document.createElement('td');
    subjectCell.textContent = email.subject;
    row.appendChild(subjectCell);
    
    // 日期
    const dateCell = document.createElement('td');
    dateCell.textContent = formatDate(email.date);
    row.appendChild(dateCell);
    
    // 附件
    const attachmentsCell = document.createElement('td');
    if (email.attachments && email.attachments.length > 0) {
      attachmentsCell.textContent = `${email.attachments.length}个附件`;
    } else {
      attachmentsCell.textContent = '无';
    }
    row.appendChild(attachmentsCell);
    
    resultsBody.appendChild(row);
  });
}

// 格式化日期
function formatDate(dateString) {
  if (!dateString) return '';
  
  try {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch (error) {
    console.error('日期格式化错误:', error);
    return dateString;
  }
}

/**
 * 安全清理函数
 */
function clearSensitiveData() {
  // 清理授权码输入框
  const authCodeInput = document.getElementById('authCode');
  if (authCodeInput) {
    authCodeInput.value = '';
  }
  
  // 清理内存中可能存在的敏感数据
  if (window.lastSearchParams) {
    delete window.lastSearchParams.auth_code;
    delete window.lastSearchParams.password;
  }
}

/**
 * 显示授权码帮助弹窗
 */
function showAuthCodeHelp() {
  const modal = document.getElementById('authCodeModal');
  if (modal) {
    modal.style.display = 'block';
  }
}

/**
 * 关闭授权码帮助弹窗
 */
function closeAuthCodeHelp() {
  const modal = document.getElementById('authCodeModal');
  if (modal) {
    modal.style.display = 'none';
  }
}

/**
 * 验证输入数据
 */
function validateInputs(email, authCode) {
  const errors = [];
  
  // 验证邮箱格式
  const emailRegex = /^[a-zA-Z0-9._%+-]+@qq\.com$/;
  if (!emailRegex.test(email)) {
    errors.push('请输入有效的QQ邮箱地址（如：<EMAIL>）');
  }
  
  // 验证授权码格式
  if (!authCode || authCode.length < 14 || authCode.length > 20) {
    errors.push('授权码格式不正确，请确认是QQ邮箱的授权码');
  }
  
  if (!/^[a-zA-Z0-9]+$/.test(authCode)) {
    errors.push('授权码只能包含字母和数字');
  }
  
  return errors;
}

/**
 * 显示错误消息
 */
function showError(message) {
  const statusElement = document.getElementById('status-message');
  if (statusElement) {
    statusElement.textContent = `❌ ${message}`;
    statusElement.style.color = '#d32f2f';
  }
  
  // 如果是运行在Electron中，也可以显示对话框
  if (typeof require !== 'undefined') {
    try {
      const { ipcRenderer } = require('electron');
      ipcRenderer.invoke('show-error', message);
    } catch (e) {
      // 如果不在Electron环境中，忽略错误
    }
  }
}

/**
 * 显示成功消息
 */
function showSuccess(message) {
  const statusElement = document.getElementById('status-message');
  if (statusElement) {
    statusElement.textContent = `✅ ${message}`;
    statusElement.style.color = '#388e3c';
  }
}

/**
 * 显示信息消息
 */
function showInfo(message) {
  const statusElement = document.getElementById('status-message');
  if (statusElement) {
    statusElement.textContent = message;
    statusElement.style.color = '#1976d2';
  }
}

// 页面卸载时清理敏感数据
window.addEventListener('beforeunload', () => {
  clearSensitiveData();
});

// 页面隐藏时清理敏感数据
document.addEventListener('visibilitychange', () => {
  if (document.hidden) {
    clearSensitiveData();
  }
});

// 点击弹窗外部关闭弹窗
window.addEventListener('click', (event) => {
  const modal = document.getElementById('authCodeModal');
  if (event.target === modal) {
    closeAuthCodeHelp();
  }
});

/**
 * 初始化搜索历史UI
 */
function initSearchHistoryUI() {
  // 这个函数将在添加搜索历史UI时实现
  console.log('搜索历史功能已初始化');
}

/**
 * 应用搜索历史
 */
function applySearchHistory(historyItem) {
  if (emailInput && historyItem.email) emailInput.value = historyItem.email;
  if (protocolSelect && historyItem.protocol) protocolSelect.value = historyItem.protocol;
  if (senderInput && historyItem.sender) senderInput.value = historyItem.sender;
  if (subjectInput && historyItem.subject) subjectInput.value = historyItem.subject;
  if (startDateInput && historyItem.start_date) startDateInput.value = historyItem.start_date;
  if (endDateInput && historyItem.end_date) endDateInput.value = historyItem.end_date;
}

// 暴露函数到全局作用域供HTML调用
window.showAuthCodeHelp = showAuthCodeHelp;
window.closeAuthCodeHelp = closeAuthCodeHelp;
window.applySearchHistory = applySearchHistory;
