<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简化版邮件搜索测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        .download-btn { background: #28a745; padding: 5px 10px; font-size: 12px; }
        .download-btn:hover { background: #218838; }
        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        th, td { padding: 10px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f2f2f2; }
        .log { background: #f8f9fa; padding: 10px; margin: 10px 0; border-radius: 4px; font-family: monospace; font-size: 12px; max-height: 200px; overflow-y: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>简化版邮件搜索测试</h1>
        
        <div class="form-group">
            <label for="email">邮箱地址:</label>
            <input type="email" id="email" value="<EMAIL>">
        </div>
        
        <div class="form-group">
            <label for="authCode">授权码:</label>
            <input type="text" id="authCode" value="vzbropcxoztvbhea">
        </div>
        
        <div class="form-group">
            <label for="sender">发件人:</label>
            <input type="text" id="sender" value="<EMAIL>">
        </div>
        
        <button onclick="testSearch()">测试搜索</button>
        <button onclick="clearLog()">清除日志</button>
        
        <div id="log" class="log"></div>
        
        <div id="results" style="display: none;">
            <h3>搜索结果:</h3>
            <table>
                <thead>
                    <tr>
                        <th>发件人</th>
                        <th>主题</th>
                        <th>日期</th>
                        <th>附件</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="resultsBody">
                </tbody>
            </table>
        </div>
    </div>

    <script>
        const MCP_SERVER_URL = 'http://localhost:3000';
        
        function log(message) {
            const logDiv = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${time}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        function formatFileSize(bytes) {
            if (!bytes || bytes === 0) return '0 B';
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(1024));
            return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
        }
        
        async function downloadAttachments(email) {
            log(`开始下载邮件 ${email.uid} 的 ${email.attachments.length} 个附件...`);
            
            try {
                const response = await fetch(`${MCP_SERVER_URL}/mcp/tools`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        tool_name: 'download_attachments',
                        arguments: {
                            email: document.getElementById('email').value,
                            auth_code: document.getElementById('authCode').value,
                            uid: email.uid,
                            attachments: email.attachments,
                            download_dir: `./downloads/simple_test_${email.uid}_${Date.now()}`
                        }
                    })
                });
                
                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.error || response.statusText);
                }
                
                const data = await response.json();
                log(`下载完成: ${data.results.length} 个文件`);
                
                data.results.forEach(file => {
                    log(`  - ${file.filename} (${formatFileSize(file.size)})`);
                });
                
            } catch (error) {
                log(`下载失败: ${error.message}`);
            }
        }
        
        async function testSearch() {
            log('=== 开始搜索测试 ===');
            
            const email = document.getElementById('email').value;
            const authCode = document.getElementById('authCode').value;
            const sender = document.getElementById('sender').value;
            
            log(`邮箱: ${email}`);
            log(`发件人: ${sender}`);
            
            try {
                log('发送搜索请求...');
                
                const response = await fetch(`${MCP_SERVER_URL}/mcp/tools`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        tool_name: 'search_qq_email',
                        arguments: {
                            email: email,
                            auth_code: authCode,
                            protocol: 'imap',
                            sender: sender,
                            start_date: '2023-11-01',
                            end_date: '2023-11-30'
                        }
                    })
                });
                
                log(`响应状态: ${response.status}`);
                
                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.error || response.statusText);
                }
                
                const data = await response.json();
                log(`搜索成功: ${data.results.length} 封邮件`);
                
                // 统计附件信息
                const emailsWithAttachments = data.results.filter(email => 
                    email.attachments && email.attachments.length > 0
                );
                log(`有附件的邮件: ${emailsWithAttachments.length} 封`);
                
                // 显示结果
                displayResults(data.results);
                
            } catch (error) {
                log(`搜索失败: ${error.message}`);
            }
        }
        
        function displayResults(results) {
            const resultsDiv = document.getElementById('results');
            const resultsBody = document.getElementById('resultsBody');
            
            resultsBody.innerHTML = '';
            
            if (results.length === 0) {
                resultsDiv.style.display = 'none';
                return;
            }
            
            resultsDiv.style.display = 'block';
            
            results.forEach((email, index) => {
                const row = document.createElement('tr');
                
                // 发件人
                const fromCell = document.createElement('td');
                fromCell.textContent = email.from || '未知';
                fromCell.style.maxWidth = '200px';
                fromCell.style.overflow = 'hidden';
                fromCell.style.textOverflow = 'ellipsis';
                row.appendChild(fromCell);
                
                // 主题
                const subjectCell = document.createElement('td');
                subjectCell.textContent = email.subject || '无主题';
                subjectCell.style.maxWidth = '300px';
                subjectCell.style.overflow = 'hidden';
                subjectCell.style.textOverflow = 'ellipsis';
                row.appendChild(subjectCell);
                
                // 日期
                const dateCell = document.createElement('td');
                dateCell.textContent = email.date ? new Date(email.date).toLocaleDateString() : '';
                row.appendChild(dateCell);
                
                // 附件
                const attachmentsCell = document.createElement('td');
                if (email.attachments && email.attachments.length > 0) {
                    attachmentsCell.innerHTML = `${email.attachments.length}个附件<br>`;
                    email.attachments.forEach(att => {
                        const attDiv = document.createElement('div');
                        attDiv.style.fontSize = '11px';
                        attDiv.style.color = '#666';
                        attDiv.textContent = `${att.filename || '未知'} (${formatFileSize(att.size || 0)})`;
                        attachmentsCell.appendChild(attDiv);
                    });
                } else {
                    attachmentsCell.textContent = '无';
                }
                row.appendChild(attachmentsCell);
                
                // 操作
                const actionsCell = document.createElement('td');
                if (email.attachments && email.attachments.length > 0) {
                    const downloadBtn = document.createElement('button');
                    downloadBtn.className = 'download-btn';
                    downloadBtn.textContent = '下载';
                    downloadBtn.onclick = () => downloadAttachments(email);
                    actionsCell.appendChild(downloadBtn);
                } else {
                    actionsCell.textContent = '-';
                }
                row.appendChild(actionsCell);
                
                resultsBody.appendChild(row);
            });
            
            log(`结果显示完成: ${results.length} 封邮件`);
        }
        
        log('简化版测试页面已加载');
    </script>
</body>
</html>
