/**
 * QQ邮箱搜索MCP工具 - 样式表
 */

/* 全局样式 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f5f5f5;
  color: #333;
  line-height: 1.6;
}

.container {
  max-width: 900px;
  margin: 0 auto;
  padding: 20px;
}

h1 {
  text-align: center;
  margin-bottom: 20px;
  color: #0078d7;
}

h2 {
  margin-bottom: 15px;
  color: #0078d7;
  font-size: 1.2rem;
}

/* 表单样式 */
.section {
  background-color: #fff;
  border-radius: 5px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 15px;
}

label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

input[type="email"],
input[type="password"],
input[type="text"],
input[type="date"],
select {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

input:focus,
select:focus {
  outline: none;
  border-color: #0078d7;
  box-shadow: 0 0 5px rgba(0, 120, 215, 0.3);
}

.help-text {
  font-size: 12px;
  margin-top: 5px;
}

.help-text a {
  color: #0078d7;
  text-decoration: none;
}

.help-text a:hover {
  text-decoration: underline;
}

/* 日期范围样式 */
.date-range {
  display: flex;
  gap: 15px;
}

.date-input {
  flex: 1;
}

/* 按钮样式 */
.actions {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-bottom: 20px;
}

button {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: bold;
  transition: background-color 0.3s;
}

.primary-btn {
  background-color: #0078d7;
  color: white;
}

.primary-btn:hover {
  background-color: #005a9e;
}

.secondary-btn {
  background-color: #5cb85c;
  color: white;
}

.secondary-btn:hover {
  background-color: #449d44;
}

button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

/* 搜索结果样式 */
.results-section {
  min-height: 300px;
}

.results-container {
  overflow-x: auto;
  max-height: 400px;
  overflow-y: auto;
}

table {
  width: 100%;
  border-collapse: collapse;
}

th, td {
  padding: 10px;
  text-align: left;
  border-bottom: 1px solid #ddd;
}

th {
  background-color: #f2f2f2;
  font-weight: bold;
}

tr:hover {
  background-color: #f5f5f5;
}

.no-results {
  text-align: center;
  padding: 20px;
  color: #888;
  display: none;
}

/* 加载指示器 */
.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px;
  display: none;
}

.spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 4px solid #0078d7;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 进度条样式 */
.progress-container {
  width: 100%;
  max-width: 300px;
  height: 8px;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  margin: 15px 0 10px 0;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #0078d7, #106ebe);
  border-radius: 4px;
  width: 0%;
  transition: width 0.3s ease;
  animation: progress-shimmer 2s infinite;
}

@keyframes progress-shimmer {
  0% { background-position: -200px 0; }
  100% { background-position: 200px 0; }
}

.progress-text {
  font-size: 12px;
  color: #666;
  margin-top: 5px;
}

/* 搜索历史样式 */
.search-history {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 5px;
  padding: 15px;
  margin-bottom: 20px;
}

.search-history h3 {
  margin: 0 0 10px 0;
  font-size: 14px;
  color: #495057;
}

.history-list {
  max-height: 200px;
  overflow-y: auto;
}

.history-item {
  background-color: #fff;
  border: 1px solid #dee2e6;
  border-radius: 3px;
  padding: 8px 12px;
  margin-bottom: 5px;
  cursor: pointer;
  transition: background-color 0.2s;
  font-size: 12px;
}

.history-item:hover {
  background-color: #e9ecef;
}

.history-item:last-child {
  margin-bottom: 0;
}

.history-item-summary {
  font-weight: 500;
  color: #495057;
}

.history-item-details {
  color: #6c757d;
  margin-top: 2px;
}

.clear-history-btn {
  background-color: #dc3545;
  color: white;
  border: none;
  padding: 5px 10px;
  border-radius: 3px;
  font-size: 12px;
  cursor: pointer;
  margin-top: 10px;
}

.clear-history-btn:hover {
  background-color: #c82333;
}

/* 通知系统样式 */
.notification-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  max-width: 400px;
}

.notification {
  background-color: #fff;
  border-radius: 5px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  margin-bottom: 10px;
  padding: 15px;
  border-left: 4px solid;
  animation: slideIn 0.3s ease-out;
  position: relative;
  cursor: pointer;
}

.notification.success {
  border-left-color: #28a745;
  background-color: #d4edda;
  color: #155724;
}

.notification.error {
  border-left-color: #dc3545;
  background-color: #f8d7da;
  color: #721c24;
}

.notification.info {
  border-left-color: #17a2b8;
  background-color: #d1ecf1;
  color: #0c5460;
}

.notification.warning {
  border-left-color: #ffc107;
  background-color: #fff3cd;
  color: #856404;
}

.notification-title {
  font-weight: 600;
  margin-bottom: 5px;
}

.notification-message {
  font-size: 14px;
  line-height: 1.4;
}

.notification-close {
  position: absolute;
  top: 10px;
  right: 15px;
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  color: inherit;
  opacity: 0.7;
}

.notification-close:hover {
  opacity: 1;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOut {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* 状态栏 */
.status-bar {
  background-color: #f2f2f2;
  padding: 10px;
  border-radius: 4px;
  font-size: 14px;
  text-align: center;
}

/* 安全提示样式 */
.security-notice {
  background-color: #e7f3ff;
  border: 1px solid #0078d7;
  border-radius: 5px;
  padding: 15px;
  margin-bottom: 20px;
}

.security-notice h3 {
  color: #0078d7;
  margin-bottom: 10px;
  font-size: 1rem;
}

.security-notice ul {
  list-style: none;
  padding: 0;
}

.security-notice li {
  margin-bottom: 8px;
  padding-left: 20px;
  position: relative;
}

.security-notice li:before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.security-notice li:has-text("✅") {
  color: #107c10;
}

.security-notice li:has-text("⚠️") {
  color: #ff8c00;
}

/* 弹窗样式 */
.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  background-color: #fff;
  margin: 5% auto;
  padding: 20px;
  border-radius: 10px;
  width: 80%;
  max-width: 600px;
  max-height: 80vh;
  overflow-y: auto;
  position: relative;
}

.close {
  position: absolute;
  top: 15px;
  right: 20px;
  color: #999;
  font-size: 24px;
  font-weight: bold;
  cursor: pointer;
}

.close:hover {
  color: #333;
}

.auth-help-content ol {
  margin: 15px 0;
  padding-left: 20px;
}

.auth-help-content li {
  margin-bottom: 8px;
}

.security-reminder {
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 5px;
  padding: 15px;
  margin-top: 20px;
}

.security-reminder strong {
  color: #856404;
}

.security-reminder ul {
  margin-top: 10px;
  padding-left: 20px;
}

.security-reminder li {
  margin-bottom: 5px;
}

/* 帮助链接样式 */
.help-text a {
  color: #0078d7;
  text-decoration: none;
  font-weight: 500;
}

.help-text a:hover {
  text-decoration: underline;
}

/* 附件下载按钮样式 */
.download-btn {
  background-color: #28a745;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 3px;
  cursor: pointer;
  font-size: 12px;
  margin-right: 5px;
  transition: background-color 0.3s;
}

.download-btn:hover {
  background-color: #218838;
}

.download-btn:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

.attachment-info {
  font-size: 12px;
  color: #666;
}

.attachment-list {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.attachment-item {
  display: flex;
  align-items: center;
  margin-bottom: 2px;
}

.attachment-filename {
  margin-right: 5px;
  font-size: 11px;
  color: #333;
}

.attachment-size {
  font-size: 10px;
  color: #888;
  margin-left: 5px;
}

/* 操作列样式 */
.actions-cell {
  white-space: nowrap;
  min-width: 120px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 10px;
  }

  .date-range {
    flex-direction: column;
    gap: 10px;
  }

  .actions {
    flex-direction: column;
  }

  button {
    width: 100%;
  }

  .attachment-list {
    max-width: 150px;
  }

  .download-btn {
    padding: 2px 6px;
    font-size: 10px;
  }
}
