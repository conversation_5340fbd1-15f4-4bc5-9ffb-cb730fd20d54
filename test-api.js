/**
 * 测试连接API
 */

async function testConnectionAPI() {
  const email = '<EMAIL>';
  const authCode = 'vzbropcxoztvbhea';
  
  console.log('测试连接API...');
  console.log(`邮箱: ${email}`);
  console.log(`授权码: ${authCode.substring(0, 4)}****`);
  
  try {
    const response = await fetch('http://localhost:3000/test-connection', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: email,
        auth_code: authCode
      })
    });
    
    console.log('响应状态:', response.status);
    console.log('响应头:', response.headers);
    
    const data = await response.json();
    console.log('响应数据:', data);
    
    if (data.success) {
      console.log('✅ 连接测试成功:', data.message);
    } else {
      console.log('❌ 连接测试失败:', data.message);
    }
    
  } catch (error) {
    console.error('API调用失败:', error.message);
  }
}

// 运行测试
if (require.main === module) {
  testConnectionAPI()
    .then(() => {
      console.log('API测试完成');
      process.exit(0);
    })
    .catch(error => {
      console.error('测试过程出错:', error);
      process.exit(1);
    });
}

module.exports = { testConnectionAPI };
