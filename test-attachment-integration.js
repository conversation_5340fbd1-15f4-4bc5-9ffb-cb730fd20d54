/**
 * 测试附件功能集成
 */

async function testAttachmentIntegration() {
  const MCP_SERVER_URL = 'http://localhost:3000';
  
  console.log('=== 测试附件功能集成 ===');
  
  try {
    // 测试1：搜索邮件（应该包含附件信息）
    console.log('\n1. 测试邮件搜索（包含附件信息）...');
    
    const searchResponse = await fetch(`${MCP_SERVER_URL}/mcp/tools`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        tool_name: 'search_qq_email',
        arguments: {
          email: '<EMAIL>',
          auth_code: 'vzbropcxoztvbhea',
          protocol: 'imap',
          sender: '<EMAIL>',
          start_date: '2023-11-01',
          end_date: '2023-11-30'
        }
      })
    });
    
    if (!searchResponse.ok) {
      throw new Error(`搜索失败: ${searchResponse.statusText}`);
    }
    
    const searchData = await searchResponse.json();
    console.log(`搜索结果: ${searchData.results.length} 封邮件`);
    
    // 检查是否有附件信息
    const emailsWithAttachments = searchData.results.filter(email => 
      email.attachments && email.attachments.length > 0
    );
    
    console.log(`有附件的邮件: ${emailsWithAttachments.length} 封`);
    
    if (emailsWithAttachments.length > 0) {
      const firstEmailWithAttachments = emailsWithAttachments[0];
      console.log('\n第一封有附件的邮件:');
      console.log(`  UID: ${firstEmailWithAttachments.uid}`);
      console.log(`  主题: ${firstEmailWithAttachments.subject}`);
      console.log(`  附件数量: ${firstEmailWithAttachments.attachments.length}`);
      
      firstEmailWithAttachments.attachments.forEach((attachment, index) => {
        console.log(`  附件 ${index + 1}:`);
        console.log(`    文件名: ${attachment.filename}`);
        console.log(`    大小: ${attachment.size} 字节`);
        console.log(`    类型: ${attachment.contentType}`);
      });
      
      // 测试2：下载附件
      console.log('\n2. 测试附件下载...');
      
      const downloadResponse = await fetch(`${MCP_SERVER_URL}/mcp/tools`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          tool_name: 'download_attachments',
          arguments: {
            email: '<EMAIL>',
            auth_code: 'vzbropcxoztvbhea',
            uid: firstEmailWithAttachments.uid,
            attachments: firstEmailWithAttachments.attachments,
            download_dir: `./downloads/test_integration_${Date.now()}`
          }
        })
      });
      
      if (!downloadResponse.ok) {
        const errorData = await downloadResponse.json();
        throw new Error(`下载失败: ${errorData.error || downloadResponse.statusText}`);
      }
      
      const downloadData = await downloadResponse.json();
      console.log(`下载结果: ${downloadData.results.length} 个文件`);
      
      downloadData.results.forEach((file, index) => {
        console.log(`  文件 ${index + 1}:`);
        console.log(`    文件名: ${file.filename}`);
        console.log(`    路径: ${file.path}`);
        console.log(`    大小: ${file.size} 字节`);
      });
      
      console.log('\n✅ 附件功能集成测试成功！');
      
    } else {
      console.log('\n⚠️ 搜索结果中没有包含附件的邮件');
      console.log('这可能是因为:');
      console.log('1. 搜索条件过于严格');
      console.log('2. 邮件结构解析有问题');
      console.log('3. 该时间段内确实没有附件邮件');
      
      // 尝试更宽泛的搜索
      console.log('\n尝试更宽泛的搜索...');
      
      const broadSearchResponse = await fetch(`${MCP_SERVER_URL}/mcp/tools`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          tool_name: 'search_qq_email',
          arguments: {
            email: '<EMAIL>',
            auth_code: 'vzbropcxoztvbhea',
            protocol: 'imap',
            sender: '<EMAIL>'
          }
        })
      });
      
      if (broadSearchResponse.ok) {
        const broadSearchData = await broadSearchResponse.json();
        const broadEmailsWithAttachments = broadSearchData.results.filter(email => 
          email.attachments && email.attachments.length > 0
        );
        
        console.log(`宽泛搜索结果: ${broadSearchData.results.length} 封邮件`);
        console.log(`有附件的邮件: ${broadEmailsWithAttachments.length} 封`);
        
        if (broadEmailsWithAttachments.length > 0) {
          console.log('找到有附件的邮件，附件功能应该可以正常工作');
        }
      }
    }
    
    // 测试3：筛选有附件的邮件
    console.log('\n3. 测试专门的附件筛选功能...');
    
    const filterResponse = await fetch(`${MCP_SERVER_URL}/mcp/tools`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        tool_name: 'filter_emails_with_attachments',
        arguments: {
          email: '<EMAIL>',
          auth_code: 'vzbropcxoztvbhea',
          sender: '<EMAIL>',
          max_emails: 50
        }
      })
    });
    
    if (!filterResponse.ok) {
      const errorData = await filterResponse.json();
      throw new Error(`筛选失败: ${errorData.error || filterResponse.statusText}`);
    }
    
    const filterData = await filterResponse.json();
    console.log(`筛选结果: ${filterData.results.length} 封有附件的邮件`);
    
    if (filterData.results.length > 0) {
      console.log('✅ 附件筛选功能正常工作');
    } else {
      console.log('⚠️ 没有找到有附件的邮件');
    }
    
  } catch (error) {
    console.error('测试失败:', error.message);
    console.error('错误详情:', error);
  }
}

// 运行测试
if (require.main === module) {
  testAttachmentIntegration()
    .then(() => {
      console.log('\n=== 附件功能集成测试完成 ===');
      process.exit(0);
    })
    .catch(error => {
      console.error('测试过程出错:', error);
      process.exit(1);
    });
}

module.exports = { testAttachmentIntegration };
