/**
 * 测试附件筛选和下载功能
 */

const attachmentService = require('./src/attachment-service');
const path = require('path');

async function testAttachments() {
  const email = '<EMAIL>';
  const authCode = 'vzbropcxoztvbhea';
  
  console.log('=== 测试QQ邮箱附件功能 ===');
  
  try {
    // 测试1：筛选有附件的邮件
    console.log('\n1. 筛选有附件的邮件...');
    
    const emailsWithAttachments = await attachmentService.filterEmailsWithAttachments(email, authCode, {
      maxEmails: 50, // 检查最近50封邮件
      // sender: '<EMAIL>', // 可选：指定发件人
      // startDate: '2024-01-01', // 可选：开始日期
      // endDate: '2024-12-31' // 可选：结束日期
    });
    
    console.log(`\n找到 ${emailsWithAttachments.length} 封有附件的邮件:`);
    
    if (emailsWithAttachments.length > 0) {
      emailsWithAttachments.forEach((email, index) => {
        console.log(`\n邮件 ${index + 1}:`);
        console.log(`  UID: ${email.uid}`);
        console.log(`  发件人: ${email.from}`);
        console.log(`  主题: ${email.subject}`);
        console.log(`  日期: ${email.date}`);
        console.log(`  附件数量: ${email.attachments.length}`);
        
        email.attachments.forEach((attachment, attIndex) => {
          console.log(`    附件 ${attIndex + 1}:`);
          console.log(`      文件名: ${attachment.filename}`);
          console.log(`      类型: ${attachment.contentType}`);
          console.log(`      大小: ${attachment.size} 字节`);
          console.log(`      编码: ${attachment.encoding}`);
        });
      });
      
      // 测试2：下载第一封邮件的附件
      if (emailsWithAttachments.length > 0) {
        console.log('\n2. 下载第一封邮件的附件...');
        
        const firstEmail = emailsWithAttachments[0];
        const downloadDir = path.join(__dirname, 'downloads', `email_${firstEmail.uid}`);
        
        console.log(`选择邮件: ${firstEmail.subject}`);
        console.log(`下载目录: ${downloadDir}`);
        
        const downloadedFiles = await attachmentService.downloadAttachments(
          email,
          authCode,
          firstEmail.uid,
          firstEmail.attachments,
          downloadDir
        );
        
        console.log(`\n下载完成！共下载 ${downloadedFiles.length} 个文件:`);
        downloadedFiles.forEach((file, index) => {
          console.log(`  ${index + 1}. ${file.filename} (${file.size} 字节)`);
          console.log(`     路径: ${file.path}`);
        });
      }
      
      // 测试3：批量下载多封邮件的附件
      if (emailsWithAttachments.length > 1) {
        console.log('\n3. 批量下载前3封邮件的附件...');
        
        const emailsToDownload = emailsWithAttachments.slice(0, 3);
        const batchDownloadDir = path.join(__dirname, 'downloads', 'batch');
        
        for (const emailData of emailsToDownload) {
          console.log(`\n下载邮件 ${emailData.uid} 的附件...`);
          
          const emailDownloadDir = path.join(batchDownloadDir, `email_${emailData.uid}`);
          
          try {
            const downloadedFiles = await attachmentService.downloadAttachments(
              email,
              authCode,
              emailData.uid,
              emailData.attachments,
              emailDownloadDir
            );
            
            console.log(`✓ 邮件 ${emailData.uid} 下载完成: ${downloadedFiles.length} 个文件`);
          } catch (error) {
            console.error(`✗ 邮件 ${emailData.uid} 下载失败:`, error.message);
          }
        }
        
        console.log('\n批量下载完成！');
      }
      
    } else {
      console.log('没有找到有附件的邮件');
      
      // 提供一些建议
      console.log('\n建议：');
      console.log('1. 增加搜索范围（maxEmails参数）');
      console.log('2. 调整日期范围');
      console.log('3. 检查是否有邮件确实包含附件');
    }
    
  } catch (error) {
    console.error('测试失败:', error.message);
    console.error('错误详情:', error);
  }
}

// 运行测试
if (require.main === module) {
  testAttachments()
    .then(() => {
      console.log('\n=== 附件测试完成 ===');
      process.exit(0);
    })
    .catch(error => {
      console.error('测试过程出错:', error);
      process.exit(1);
    });
}

module.exports = { testAttachments };
