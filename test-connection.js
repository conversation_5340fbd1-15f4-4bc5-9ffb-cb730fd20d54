/**
 * QQ邮箱连接测试工具
 */
const Imap = require('imap');

// 测试配置
const TEST_CONFIGS = [
  {
    name: '标准配置',
    config: {
      host: 'imap.qq.com',
      port: 993,
      tls: true,
      tlsOptions: { 
        rejectUnauthorized: false
      },
      connTimeout: 60000,
      authTimeout: 60000,
      debug: console.log
    }
  },
  {
    name: '简化配置',
    config: {
      host: 'imap.qq.com',
      port: 993,
      tls: true,
      connTimeout: 30000,
      authTimeout: 30000
    }
  },
  {
    name: '兼容配置',
    config: {
      host: 'imap.qq.com',
      port: 993,
      tls: true,
      tlsOptions: { 
        rejectUnauthorized: false,
        secureProtocol: 'TLSv1_method'
      },
      connTimeout: 60000,
      authTimeout: 60000
    }
  }
];

async function testConnection(email, authCode, configIndex = 0) {
  const testConfig = TEST_CONFIGS[configIndex];
  console.log(`\n=== 测试配置: ${testConfig.name} ===`);
  
  const imap = new Imap({
    ...testConfig.config,
    user: email,
    password: authCode
  });
  
  return new Promise((resolve, reject) => {
    const timeout = setTimeout(() => {
      imap.end();
      reject(new Error('连接超时'));
    }, 30000);
    
    imap.once('ready', () => {
      clearTimeout(timeout);
      console.log('✓ 连接成功！');
      imap.end();
      resolve(true);
    });
    
    imap.once('error', (err) => {
      clearTimeout(timeout);
      console.log(`✗ 连接失败: ${err.message}`);
      resolve(false);
    });
    
    imap.connect();
  });
}

async function testAllConfigs(email, authCode) {
  console.log('开始测试QQ邮箱连接...');
  console.log(`邮箱: ${email}`);
  console.log(`授权码: ${authCode.substring(0, 4)}****`);
  
  for (let i = 0; i < TEST_CONFIGS.length; i++) {
    try {
      const success = await testConnection(email, authCode, i);
      if (success) {
        console.log(`\n🎉 找到可用配置: ${TEST_CONFIGS[i].name}`);
        return i;
      }
    } catch (error) {
      console.log(`配置 ${TEST_CONFIGS[i].name} 测试失败: ${error.message}`);
    }
    
    // 等待一秒再测试下一个配置
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log('\n❌ 所有配置都无法连接');
  return -1;
}

// 如果直接运行此文件
if (require.main === module) {
  const args = process.argv.slice(2);
  if (args.length < 2) {
    console.log('用法: node test-connection.js <邮箱> <授权码>');
    console.log('例如: node test-connection.js <EMAIL> abcdefghijklmnop');
    process.exit(1);
  }
  
  const [email, authCode] = args;
  testAllConfigs(email, authCode)
    .then(configIndex => {
      if (configIndex >= 0) {
        console.log(`\n建议使用配置 ${configIndex}: ${TEST_CONFIGS[configIndex].name}`);
      } else {
        console.log('\n请检查：');
        console.log('1. 邮箱地址是否正确');
        console.log('2. 授权码是否有效');
        console.log('3. QQ邮箱是否已开启IMAP服务');
        console.log('4. 网络连接是否正常');
      }
      process.exit(0);
    })
    .catch(error => {
      console.error('测试过程出错:', error);
      process.exit(1);
    });
}

module.exports = { testConnection, testAllConfigs, TEST_CONFIGS };
