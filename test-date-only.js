/**
 * 测试只使用日期条件的搜索
 */

async function testDateOnly() {
  // 清除模块缓存
  delete require.cache[require.resolve('./src/email-service')];
  const emailService = require('./src/email-service');
  
  const email = '<EMAIL>';
  const authCode = 'vzbropcxoztvbhea';
  
  console.log('=== 测试只使用日期条件的搜索 ===');
  
  try {
    // 测试1：只搜索2023-11-27的邮件
    console.log('\n1. 只搜索2023-11-27的邮件...');
    const results1 = await emailService.searchEmails({
      email: email,
      auth_code: authCode,
      protocol: 'imap',
      startDate: '2023-11-27',
      endDate: '2023-11-27'
    });
    
    console.log(`结果: ${results1.length} 封邮件`);
    
    if (results1.length > 0) {
      console.log('✅ 找到邮件！');
      results1.forEach((mail, index) => {
        console.log(`  ${index + 1}. ${mail.date} - ${mail.from}`);
      });
    } else {
      console.log('❌ 没有找到邮件');
    }
    
    // 测试2：只搜索2024-01-29的邮件
    console.log('\n2. 只搜索2024-01-29的邮件...');
    const results2 = await emailService.searchEmails({
      email: email,
      auth_code: authCode,
      protocol: 'imap',
      startDate: '2024-01-29',
      endDate: '2024-01-29'
    });
    
    console.log(`结果: ${results2.length} 封邮件`);
    
    if (results2.length > 0) {
      console.log('✅ 找到邮件！');
      results2.forEach((mail, index) => {
        console.log(`  ${index + 1}. ${mail.date} - ${mail.from}`);
      });
    } else {
      console.log('❌ 没有找到邮件');
    }
    
    // 测试3：搜索2023年11月整月
    console.log('\n3. 搜索2023年11月整月...');
    const results3 = await emailService.searchEmails({
      email: email,
      auth_code: authCode,
      protocol: 'imap',
      startDate: '2023-11-01',
      endDate: '2023-11-30'
    });
    
    console.log(`结果: ${results3.length} 封邮件`);
    
    if (results3.length > 0) {
      console.log('✅ 找到邮件！');
      // 检查是否包含我们期望的发件人
      const targetEmails = results3.filter(mail => 
        mail.from.includes('hxuan25') || mail.from.includes('concerthall.com.cn')
      );
      
      if (targetEmails.length > 0) {
        console.log(`其中包含目标发件人的邮件: ${targetEmails.length} 封`);
        targetEmails.forEach((mail, index) => {
          console.log(`  ${index + 1}. ${mail.date} - ${mail.from}`);
        });
      } else {
        console.log('但不包含目标发件人的邮件');
      }
    }
    
    // 测试4：搜索2024年1月整月
    console.log('\n4. 搜索2024年1月整月...');
    const results4 = await emailService.searchEmails({
      email: email,
      auth_code: authCode,
      protocol: 'imap',
      startDate: '2024-01-01',
      endDate: '2024-01-31'
    });
    
    console.log(`结果: ${results4.length} 封邮件`);
    
    if (results4.length > 0) {
      console.log('✅ 找到邮件！');
      // 检查是否包含我们期望的发件人
      const targetEmails = results4.filter(mail => 
        mail.from.includes('hxuan25') || mail.from.includes('concerthall.com.cn')
      );
      
      if (targetEmails.length > 0) {
        console.log(`其中包含目标发件人的邮件: ${targetEmails.length} 封`);
        targetEmails.forEach((mail, index) => {
          console.log(`  ${index + 1}. ${mail.date} - ${mail.from}`);
        });
      } else {
        console.log('但不包含目标发件人的邮件');
      }
    }
    
  } catch (error) {
    console.error('测试失败:', error.message);
  }
}

// 运行测试
if (require.main === module) {
  testDateOnly()
    .then(() => {
      console.log('\n只使用日期条件的搜索测试完成');
      process.exit(0);
    })
    .catch(error => {
      console.error('测试过程出错:', error);
      process.exit(1);
    });
}

module.exports = { testDateOnly };
