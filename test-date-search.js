/**
 * 测试日期搜索功能
 */

async function testDateSearch() {
  // 清除模块缓存
  delete require.cache[require.resolve('./src/email-service')];
  const emailService = require('./src/email-service');
  
  const email = '<EMAIL>';
  const authCode = 'vzbropcxoztvbhea';
  
  console.log('=== 测试日期搜索功能 ===');
  
  try {
    // 测试1：获取最近邮件，查看实际日期
    console.log('\n1. 获取最近邮件，查看实际日期...');
    const recentResults = await emailService.searchEmails({
      email: email,
      auth_code: authCode,
      protocol: 'imap'
    });
    
    if (recentResults.length > 0) {
      console.log('最近5封邮件的日期:');
      recentResults.slice(0, 5).forEach((mail, index) => {
        console.log(`  ${index + 1}. ${mail.date} - ${mail.from}`);
      });
    }
    
    // 测试2：测试不同的日期格式
    const testDates = [
      { name: '今天', start: '2025-07-10', end: '2025-07-10' },
      { name: '最近一周', start: '2025-07-03', end: '2025-07-10' },
      { name: '最近一个月', start: '2025-06-10', end: '2025-07-10' },
      { name: '最近三个月', start: '2025-04-10', end: '2025-07-10' },
      { name: '今年', start: '2025-01-01', end: '2025-07-10' }
    ];
    
    for (const testDate of testDates) {
      console.log(`\n2. 测试${testDate.name} (${testDate.start} 到 ${testDate.end})...`);
      
      try {
        const results = await emailService.searchEmails({
          email: email,
          auth_code: authCode,
          protocol: 'imap',
          startDate: testDate.start,
          endDate: testDate.end
        });
        
        console.log(`   结果: ${results.length} 封邮件`);
        
        if (results.length > 0 && results.length <= 3) {
          results.forEach((mail, index) => {
            console.log(`     ${index + 1}. ${mail.date} - ${mail.from.substring(0, 50)}`);
          });
        }
      } catch (error) {
        console.log(`   错误: ${error.message}`);
      }
    }
    
    // 测试3：测试IMAP日期格式
    console.log('\n3. 测试IMAP原生日期搜索...');
    await testIMAPDateFormats(email, authCode);
    
  } catch (error) {
    console.error('日期搜索测试失败:', error.message);
  }
}

async function testIMAPDateFormats(email, authCode) {
  const Imap = require('imap');
  
  const imap = new Imap({
    host: 'imap.qq.com',
    port: 993,
    tls: true,
    user: email,
    password: authCode,
    connTimeout: 30000,
    authTimeout: 30000
  });
  
  return new Promise((resolve, reject) => {
    imap.once('ready', () => {
      console.log('IMAP连接就绪，测试日期格式...');
      
      imap.openBox('INBOX', true, (err, box) => {
        if (err) {
          console.error('打开收件箱失败:', err);
          imap.end();
          reject(err);
          return;
        }
        
        // 测试不同的日期格式
        const dateFormats = [
          { name: 'SINCE 今天', criteria: ['SINCE', new Date('2025-07-10')] },
          { name: 'SINCE 昨天', criteria: ['SINCE', new Date('2025-07-09')] },
          { name: 'SINCE 一周前', criteria: ['SINCE', new Date('2025-07-03')] },
          { name: 'SINCE 一个月前', criteria: ['SINCE', new Date('2025-06-10')] },
          { name: 'ON 今天', criteria: ['ON', new Date('2025-07-10')] },
          { name: 'BEFORE 明天', criteria: ['BEFORE', new Date('2025-07-11')] }
        ];
        
        let testIndex = 0;
        
        function testNextFormat() {
          if (testIndex >= dateFormats.length) {
            imap.end();
            resolve();
            return;
          }
          
          const format = dateFormats[testIndex];
          console.log(`\n测试格式: ${format.name}`);
          console.log(`搜索条件: [${format.criteria[0]}, ${format.criteria[1]}]`);
          
          imap.search([format.criteria], (err, results) => {
            if (err) {
              console.log(`  错误: ${err.message}`);
            } else {
              console.log(`  结果: ${results.length} 封邮件`);
            }
            
            testIndex++;
            setTimeout(testNextFormat, 1000); // 等待1秒再测试下一个
          });
        }
        
        testNextFormat();
      });
    });
    
    imap.once('error', (err) => {
      console.error('IMAP错误:', err);
      reject(err);
    });
    
    imap.connect();
  });
}

// 运行测试
if (require.main === module) {
  testDateSearch()
    .then(() => {
      console.log('\n日期搜索测试完成');
      process.exit(0);
    })
    .catch(error => {
      console.error('测试过程出错:', error);
      process.exit(1);
    });
}

module.exports = { testDateSearch };
