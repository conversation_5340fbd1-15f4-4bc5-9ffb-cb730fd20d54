/**
 * 测试修复后的日期搜索
 */

async function testFixedDateSearch() {
  // 清除模块缓存
  delete require.cache[require.resolve('./src/email-service')];
  const emailService = require('./src/email-service');
  
  const email = '<EMAIL>';
  const authCode = 'vzbropcxoztvbhea';
  
  console.log('=== 测试修复后的日期搜索 ===');
  
  try {
    // 基于实际邮件日期进行测试（6月21日）
    const testCases = [
      {
        name: '搜索6月21日（实际有邮件的日期）',
        startDate: '2025-06-21',
        endDate: '2025-06-21'
      },
      {
        name: '搜索6月20-22日（包含6月21日的范围）',
        startDate: '2025-06-20',
        endDate: '2025-06-22'
      },
      {
        name: '搜索6月整月',
        startDate: '2025-06-01',
        endDate: '2025-06-30'
      },
      {
        name: '搜索6月21日之后',
        startDate: '2025-06-21',
        endDate: undefined
      },
      {
        name: '搜索6月21日之前',
        startDate: undefined,
        endDate: '2025-06-21'
      },
      {
        name: '搜索7月10日（应该没有邮件）',
        startDate: '2025-07-10',
        endDate: '2025-07-10'
      }
    ];
    
    for (const testCase of testCases) {
      console.log(`\n测试: ${testCase.name}`);
      console.log(`日期范围: ${testCase.startDate || '不限'} 到 ${testCase.endDate || '不限'}`);
      
      try {
        const results = await emailService.searchEmails({
          email: email,
          auth_code: authCode,
          protocol: 'imap',
          startDate: testCase.startDate,
          endDate: testCase.endDate
        });
        
        console.log(`结果: ${results.length} 封邮件`);
        
        if (results.length > 0 && results.length <= 5) {
          console.log('邮件详情:');
          results.forEach((mail, index) => {
            console.log(`  ${index + 1}. ${mail.date} - ${mail.from.substring(0, 50)}...`);
          });
        }
        
      } catch (error) {
        console.log(`错误: ${error.message}`);
      }
    }
    
    // 测试组合搜索（日期 + 发件人）
    console.log('\n=== 测试组合搜索（日期 + 发件人）===');
    
    const comboTests = [
      {
        name: '6月21日 + 特定发件人',
        startDate: '2025-06-21',
        endDate: '2025-06-21',
        sender: '<EMAIL>'
      },
      {
        name: '6月整月 + 京东',
        startDate: '2025-06-01',
        endDate: '2025-06-30',
        sender: 'jd.com'
      }
    ];
    
    for (const test of comboTests) {
      console.log(`\n测试: ${test.name}`);
      
      try {
        const results = await emailService.searchEmails({
          email: email,
          auth_code: authCode,
          protocol: 'imap',
          startDate: test.startDate,
          endDate: test.endDate,
          sender: test.sender
        });
        
        console.log(`结果: ${results.length} 封邮件`);
        
        if (results.length > 0) {
          console.log('找到匹配邮件:');
          results.slice(0, 3).forEach((mail, index) => {
            console.log(`  ${index + 1}. ${mail.date} - ${mail.from}`);
          });
        }
        
      } catch (error) {
        console.log(`错误: ${error.message}`);
      }
    }
    
  } catch (error) {
    console.error('测试失败:', error.message);
  }
}

// 运行测试
if (require.main === module) {
  testFixedDateSearch()
    .then(() => {
      console.log('\n修复后的日期搜索测试完成');
      process.exit(0);
    })
    .catch(error => {
      console.error('测试过程出错:', error);
      process.exit(1);
    });
}

module.exports = { testFixedDateSearch };
