/**
 * 测试修复后的搜索条件
 */

async function testFixedSearch() {
  // 清除模块缓存
  delete require.cache[require.resolve('./src/email-service')];
  const emailService = require('./src/email-service');
  
  const email = '<EMAIL>';
  const authCode = 'vzbropcxoztvbhea';
  
  console.log('=== 测试修复后的搜索条件 ===');
  
  try {
    // 测试1：发件人 + 日期组合搜索（之前失败的）
    console.log('\n1. 测试发件人 + 日期组合搜索...');
    
    const testCases = [
      {
        name: '完整格式发件人 + 2023-11-27',
        sender: 'hxuan25 <<EMAIL>>',
        startDate: '2023-11-27',
        endDate: '2023-11-27'
      },
      {
        name: '邮箱地址 + 2023-11-27',
        sender: '<EMAIL>',
        startDate: '2023-11-27',
        endDate: '2023-11-27'
      },
      {
        name: '用户名 + 2023-11-27',
        sender: 'hxuan25',
        startDate: '2023-11-27',
        endDate: '2023-11-27'
      },
      {
        name: '完整格式发件人 + 2024-01-29',
        sender: 'hxuan25 <<EMAIL>>',
        startDate: '2024-01-29',
        endDate: '2024-01-29'
      }
    ];
    
    for (const testCase of testCases) {
      console.log(`\n测试: ${testCase.name}`);
      
      try {
        const results = await emailService.searchEmails({
          email: email,
          auth_code: authCode,
          protocol: 'imap',
          sender: testCase.sender,
          startDate: testCase.startDate,
          endDate: testCase.endDate
        });
        
        console.log(`结果: ${results.length} 封邮件`);
        
        if (results.length > 0) {
          console.log('✅ 成功找到邮件！');
          results.forEach((mail, index) => {
            console.log(`  ${index + 1}. ${mail.date} - ${mail.from}`);
          });
        } else {
          console.log('❌ 仍然没有找到邮件');
        }
        
      } catch (error) {
        console.log(`错误: ${error.message}`);
      }
    }
    
    // 测试2：其他组合
    console.log('\n2. 测试其他日期组合...');
    
    const dateTests = [
      {
        name: '2023年11月整月',
        startDate: '2023-11-01',
        endDate: '2023-11-30'
      },
      {
        name: '2023年11月27日到2024年1月29日',
        startDate: '2023-11-27',
        endDate: '2024-01-29'
      }
    ];
    
    for (const dateTest of dateTests) {
      console.log(`\n测试: ${dateTest.name}`);
      
      try {
        const results = await emailService.searchEmails({
          email: email,
          auth_code: authCode,
          protocol: 'imap',
          sender: 'hxuan25 <<EMAIL>>',
          startDate: dateTest.startDate,
          endDate: dateTest.endDate
        });
        
        console.log(`结果: ${results.length} 封邮件`);
        
        if (results.length > 0) {
          console.log('✅ 成功找到邮件！');
        }
        
      } catch (error) {
        console.log(`错误: ${error.message}`);
      }
    }
    
  } catch (error) {
    console.error('测试失败:', error.message);
  }
}

// 运行测试
if (require.main === module) {
  testFixedSearch()
    .then(() => {
      console.log('\n修复后的搜索测试完成');
      process.exit(0);
    })
    .catch(error => {
      console.error('测试过程出错:', error);
      process.exit(1);
    });
}

module.exports = { testFixedSearch };
