<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端附件功能测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 1000px; margin: 0 auto; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .results { margin-top: 20px; }
        table { width: 100%; border-collapse: collapse; margin-top: 10px; }
        th, td { padding: 10px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f2f2f2; }
        .log { background: #f1f1f1; padding: 10px; margin: 10px 0; border-radius: 4px; font-family: monospace; white-space: pre-wrap; max-height: 300px; overflow-y: auto; }
        .download-btn { background-color: #28a745; color: white; border: none; padding: 4px 8px; border-radius: 3px; cursor: pointer; font-size: 12px; }
        .download-btn:hover { background-color: #218838; }
        .attachment-info { font-size: 12px; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <h1>前端附件功能测试</h1>
        
        <form id="testForm">
            <div class="form-group">
                <label for="email">邮箱地址:</label>
                <input type="email" id="email" value="<EMAIL>" required>
            </div>
            
            <div class="form-group">
                <label for="authCode">授权码:</label>
                <input type="text" id="authCode" value="vzbropcxoztvbhea" required>
            </div>
            
            <div class="form-group">
                <label for="sender">发件人:</label>
                <input type="text" id="sender" value="<EMAIL>">
            </div>
            
            <div class="form-group">
                <label for="startDate">开始日期:</label>
                <input type="date" id="startDate" value="2023-11-01">
            </div>
            
            <div class="form-group">
                <label for="endDate">结束日期:</label>
                <input type="date" id="endDate" value="2023-11-30">
            </div>
            
            <button type="submit">搜索邮件</button>
        </form>
        
        <div id="results" class="results" style="display: none;">
            <h3>搜索结果:</h3>
            <table id="resultsTable">
                <thead>
                    <tr>
                        <th>发件人</th>
                        <th>主题</th>
                        <th>日期</th>
                        <th>附件</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="resultsBody">
                </tbody>
            </table>
        </div>
        
        <div id="logs" class="log" style="display: none;"></div>
    </div>

    <script>
        const MCP_SERVER_URL = 'http://localhost:3000';
        
        function log(message) {
            const logsDiv = document.getElementById('logs');
            logsDiv.style.display = 'block';
            logsDiv.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
            console.log(message);
        }
        
        function formatFileSize(bytes) {
            if (!bytes || bytes === 0) return '0 B';
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(1024));
            return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
        }
        
        function formatDate(dateString) {
            if (!dateString) return '';
            try {
                const date = new Date(dateString);
                return date.toLocaleString('zh-CN');
            } catch (error) {
                return dateString;
            }
        }
        
        async function downloadAttachments(email) {
            log(`开始下载邮件 ${email.uid} 的附件...`);
            
            const emailAddress = document.getElementById('email').value;
            const authCode = document.getElementById('authCode').value;
            
            try {
                const downloadDir = `./downloads/frontend_test_${email.uid}_${Date.now()}`;
                
                const response = await fetch(`${MCP_SERVER_URL}/mcp/tools`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        tool_name: 'download_attachments',
                        arguments: {
                            email: emailAddress,
                            auth_code: authCode,
                            uid: email.uid,
                            attachments: email.attachments,
                            download_dir: downloadDir
                        }
                    })
                });
                
                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.error || `下载失败: ${response.statusText}`);
                }
                
                const data = await response.json();
                log(`下载完成: ${data.results.length} 个文件`);
                
                data.results.forEach((file, index) => {
                    log(`  文件 ${index + 1}: ${file.filename} (${formatFileSize(file.size)})`);
                });
                
                alert(`成功下载 ${data.results.length} 个附件到 ${downloadDir}`);
                
            } catch (error) {
                log(`下载失败: ${error.message}`);
                alert(`下载失败: ${error.message}`);
            }
        }
        
        async function searchEmails(event) {
            event.preventDefault();
            
            document.getElementById('logs').textContent = '';
            log('开始搜索邮件...');
            
            const email = document.getElementById('email').value;
            const authCode = document.getElementById('authCode').value;
            const sender = document.getElementById('sender').value;
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            
            const searchParams = {
                email: email,
                auth_code: authCode,
                protocol: 'imap',
                sender: sender || undefined,
                start_date: startDate || undefined,
                end_date: endDate || undefined
            };
            
            log('搜索参数: ' + JSON.stringify(searchParams, null, 2));
            
            try {
                const response = await fetch(`${MCP_SERVER_URL}/mcp/tools`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        tool_name: 'search_qq_email',
                        arguments: searchParams
                    })
                });
                
                log(`响应状态: ${response.status} ${response.statusText}`);
                
                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.error || `搜索失败: ${response.statusText}`);
                }
                
                const data = await response.json();
                log('搜索响应: ' + JSON.stringify(data, null, 2));
                
                const results = data.results || [];
                log(`搜索结果数量: ${results.length}`);
                
                // 显示结果
                displayResults(results);
                
            } catch (error) {
                log('搜索错误: ' + error.message);
                alert('搜索失败: ' + error.message);
            }
        }
        
        function displayResults(results) {
            const resultsDiv = document.getElementById('results');
            const resultsBody = document.getElementById('resultsBody');
            
            resultsBody.innerHTML = '';
            
            if (results.length === 0) {
                resultsDiv.style.display = 'none';
                log('没有找到匹配的邮件');
                return;
            }
            
            resultsDiv.style.display = 'block';
            log(`开始显示 ${results.length} 封邮件...`);
            
            results.forEach((email, index) => {
                log(`处理邮件 ${index + 1}: UID=${email.uid}, 附件=${email.attachments ? email.attachments.length : 0}个`);
                
                const row = document.createElement('tr');
                
                // 发件人
                const fromCell = document.createElement('td');
                fromCell.textContent = email.from || '未知发件人';
                row.appendChild(fromCell);
                
                // 主题
                const subjectCell = document.createElement('td');
                subjectCell.textContent = email.subject || '无主题';
                row.appendChild(subjectCell);
                
                // 日期
                const dateCell = document.createElement('td');
                dateCell.textContent = formatDate(email.date);
                row.appendChild(dateCell);
                
                // 附件
                const attachmentsCell = document.createElement('td');
                if (email.attachments && email.attachments.length > 0) {
                    const attachmentInfo = document.createElement('div');
                    attachmentInfo.className = 'attachment-info';
                    
                    email.attachments.forEach((attachment, attIndex) => {
                        const attDiv = document.createElement('div');
                        attDiv.textContent = `${attachment.filename || `附件${attIndex + 1}`} (${formatFileSize(attachment.size || 0)})`;
                        attachmentInfo.appendChild(attDiv);
                    });
                    
                    attachmentsCell.appendChild(attachmentInfo);
                } else {
                    attachmentsCell.textContent = '无';
                }
                row.appendChild(attachmentsCell);
                
                // 操作
                const actionsCell = document.createElement('td');
                if (email.attachments && email.attachments.length > 0) {
                    const downloadBtn = document.createElement('button');
                    downloadBtn.className = 'download-btn';
                    downloadBtn.textContent = '下载附件';
                    downloadBtn.onclick = () => downloadAttachments(email);
                    actionsCell.appendChild(downloadBtn);
                } else {
                    actionsCell.textContent = '-';
                }
                row.appendChild(actionsCell);
                
                resultsBody.appendChild(row);
            });
            
            log('邮件显示完成');
        }
        
        document.getElementById('testForm').addEventListener('submit', searchEmails);
        
        log('前端附件功能测试页面已加载');
    </script>
</body>
</html>
