<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端API测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .results { margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 4px; }
        .error { color: red; }
        .success { color: green; }
        .log { background: #f1f1f1; padding: 10px; margin: 10px 0; border-radius: 4px; font-family: monospace; white-space: pre-wrap; }
    </style>
</head>
<body>
    <div class="container">
        <h1>前端API测试</h1>
        
        <form id="testForm">
            <div class="form-group">
                <label for="email">邮箱地址:</label>
                <input type="email" id="email" value="<EMAIL>" required>
            </div>
            
            <div class="form-group">
                <label for="authCode">授权码:</label>
                <input type="text" id="authCode" value="vzbropcxoztvbhea" required>
            </div>
            
            <div class="form-group">
                <label for="sender">发件人 (可选):</label>
                <input type="text" id="sender" value="<EMAIL>">
            </div>
            
            <div class="form-group">
                <label for="startDate">开始日期 (可选):</label>
                <input type="date" id="startDate" value="2023-11-01">
            </div>
            
            <div class="form-group">
                <label for="endDate">结束日期 (可选):</label>
                <input type="date" id="endDate" value="2023-11-30">
            </div>
            
            <button type="submit">测试搜索</button>
        </form>
        
        <div id="results" class="results" style="display: none;">
            <h3>测试结果:</h3>
            <div id="resultContent"></div>
        </div>
        
        <div id="logs" class="log" style="display: none;"></div>
    </div>

    <script>
        const MCP_SERVER_URL = 'http://localhost:3000';
        
        function log(message) {
            const logsDiv = document.getElementById('logs');
            logsDiv.style.display = 'block';
            logsDiv.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
            console.log(message);
        }
        
        function showResult(content, isError = false) {
            const resultsDiv = document.getElementById('results');
            const resultContent = document.getElementById('resultContent');
            
            resultsDiv.style.display = 'block';
            resultContent.innerHTML = `<div class="${isError ? 'error' : 'success'}">${content}</div>`;
        }
        
        async function testSearch(event) {
            event.preventDefault();
            
            // 清空之前的日志
            document.getElementById('logs').textContent = '';
            
            log('开始测试搜索...');
            
            const email = document.getElementById('email').value;
            const authCode = document.getElementById('authCode').value;
            const sender = document.getElementById('sender').value;
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            
            const searchParams = {
                email: email,
                auth_code: authCode,
                protocol: 'imap',
                sender: sender || undefined,
                start_date: startDate || undefined,
                end_date: endDate || undefined
            };
            
            log('搜索参数: ' + JSON.stringify(searchParams, null, 2));
            
            try {
                log('发送API请求...');
                
                const response = await fetch(`${MCP_SERVER_URL}/mcp/tools`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        tool_name: 'search_qq_email',
                        arguments: searchParams
                    })
                });
                
                log(`响应状态: ${response.status} ${response.statusText}`);
                
                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.error || `请求失败: ${response.statusText}`);
                }
                
                const data = await response.json();
                log('响应数据: ' + JSON.stringify(data, null, 2));
                
                const results = data.results || [];
                log(`搜索结果数量: ${results.length}`);
                
                if (results.length > 0) {
                    let resultHtml = `<h4>找到 ${results.length} 封邮件:</h4><ul>`;
                    results.slice(0, 5).forEach((email, index) => {
                        resultHtml += `<li><strong>${email.from}</strong> - ${email.subject} (${email.date})</li>`;
                    });
                    if (results.length > 5) {
                        resultHtml += `<li>... 还有 ${results.length - 5} 封邮件</li>`;
                    }
                    resultHtml += '</ul>';
                    showResult(resultHtml);
                } else {
                    showResult('没有找到匹配的邮件', true);
                }
                
            } catch (error) {
                log('错误: ' + error.message);
                showResult('搜索失败: ' + error.message, true);
            }
        }
        
        document.getElementById('testForm').addEventListener('submit', testSearch);
        
        log('前端测试页面已加载');
    </script>
</body>
</html>
