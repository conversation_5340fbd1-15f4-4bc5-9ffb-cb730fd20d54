/**
 * 使用nodemailer测试QQ邮箱连接
 * 这个测试可以验证邮箱凭证是否正确
 */
const nodemailer = require('nodemailer');

async function testQQEmailWithNodemailer(email, authCode) {
  console.log('使用nodemailer测试QQ邮箱连接...');
  console.log(`邮箱: ${email}`);
  console.log(`授权码: ${authCode.substring(0, 4)}****`);
  
  // 创建SMTP传输器
  const transporter = nodemailer.createTransporter({
    host: 'smtp.qq.com',
    port: 465,
    secure: true, // 使用SSL
    auth: {
      user: email,
      pass: authCode
    },
    // 调试选项
    debug: true,
    logger: true
  });
  
  try {
    // 验证连接
    console.log('正在验证SMTP连接...');
    await transporter.verify();
    console.log('✅ SMTP连接验证成功！');
    
    // 如果SMTP成功，说明凭证正确，IMAP应该也能工作
    return true;
  } catch (error) {
    console.log('❌ SMTP连接验证失败:', error.message);
    
    if (error.message.includes('Invalid login')) {
      console.log('错误原因：邮箱地址或授权码不正确');
    } else if (error.message.includes('authentication')) {
      console.log('错误原因：认证失败，请检查授权码');
    } else {
      console.log('错误原因：', error.message);
    }
    
    return false;
  }
}

// 如果直接运行此文件
if (require.main === module) {
  const args = process.argv.slice(2);
  if (args.length < 2) {
    console.log('用法: node test-nodemailer.js <邮箱> <授权码>');
    console.log('例如: node test-nodemailer.js <EMAIL> abcdefghijklmnop');
    process.exit(1);
  }
  
  const [email, authCode] = args;
  testQQEmailWithNodemailer(email, authCode)
    .then(success => {
      if (success) {
        console.log('\n🎉 邮箱凭证验证成功！');
        console.log('如果IMAP仍然无法连接，可能是IMAP库的兼容性问题。');
      } else {
        console.log('\n❌ 邮箱凭证验证失败！');
        console.log('请检查：');
        console.log('1. 邮箱地址是否正确');
        console.log('2. 授权码是否有效');
        console.log('3. QQ邮箱是否已开启SMTP服务');
      }
      process.exit(0);
    })
    .catch(error => {
      console.error('测试过程出错:', error);
      process.exit(1);
    });
}

module.exports = { testQQEmailWithNodemailer };
