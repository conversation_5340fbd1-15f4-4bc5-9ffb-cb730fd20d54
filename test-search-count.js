/**
 * 测试搜索结果数量
 */

async function testSearchCount() {
  // 清除模块缓存
  delete require.cache[require.resolve('./src/email-service')];
  const emailService = require('./src/email-service');
  
  const email = '<EMAIL>';
  const authCode = 'vzbropcxoztvbhea';
  
  console.log('=== 测试搜索结果数量 ===');
  
  try {
    // 测试1：发件人搜索（应该有很多结果）
    console.log('\n1. 测试发件人搜索（期望多个结果）...');
    const results1 = await emailService.searchEmails({
      email: email,
      auth_code: authCode,
      protocol: 'imap',
      sender: '<EMAIL>'
    });
    
    console.log(`发件人搜索结果: ${results1.length} 封邮件`);
    
    // 测试2：发件人 + 日期范围搜索
    console.log('\n2. 测试发件人 + 日期范围搜索...');
    const results2 = await emailService.searchEmails({
      email: email,
      auth_code: authCode,
      protocol: 'imap',
      sender: '<EMAIL>',
      startDate: '2023-11-01',
      endDate: '2023-11-30'
    });
    
    console.log(`组合搜索结果: ${results2.length} 封邮件`);
    
    if (results2.length > 0) {
      console.log('前5封邮件:');
      results2.slice(0, 5).forEach((mail, index) => {
        console.log(`  ${index + 1}. ${mail.date} - ${mail.from} - ${mail.subject}`);
      });
    }
    
    // 测试3：更大的日期范围
    console.log('\n3. 测试更大的日期范围...');
    const results3 = await emailService.searchEmails({
      email: email,
      auth_code: authCode,
      protocol: 'imap',
      sender: '<EMAIL>',
      startDate: '2023-01-01',
      endDate: '2024-12-31'
    });
    
    console.log(`大范围搜索结果: ${results3.length} 封邮件`);
    
    if (results3.length > 0) {
      console.log('邮件日期分布:');
      const dates = results3.map(mail => mail.date.substring(0, 10)).sort();
      const uniqueDates = [...new Set(dates)];
      console.log(`日期范围: ${uniqueDates[0]} 到 ${uniqueDates[uniqueDates.length - 1]}`);
      console.log(`涉及日期数: ${uniqueDates.length} 天`);
    }
    
  } catch (error) {
    console.error('测试失败:', error.message);
  }
}

// 运行测试
if (require.main === module) {
  testSearchCount()
    .then(() => {
      console.log('\n搜索结果数量测试完成');
      process.exit(0);
    })
    .catch(error => {
      console.error('测试过程出错:', error);
      process.exit(1);
    });
}

module.exports = { testSearchCount };
