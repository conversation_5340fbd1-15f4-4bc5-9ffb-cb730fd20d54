/**
 * 测试发件人格式对搜索的影响
 */

async function testSenderFormat() {
  // 清除模块缓存
  delete require.cache[require.resolve('./src/email-service')];
  const emailService = require('./src/email-service');
  
  const email = '<EMAIL>';
  const authCode = 'vzbropcxoztvbhea';
  
  console.log('=== 测试发件人格式对搜索的影响 ===');
  
  // 测试不同的发件人格式
  const senderFormats = [
    'hxuan25 <<EMAIL>>',  // 完整格式（用户输入）
    '<EMAIL>',            // 只有邮箱地址
    'hxuan25',                               // 只有用户名
    'concerthall.com.cn'                     // 只有域名
  ];
  
  try {
    for (const sender of senderFormats) {
      console.log(`\n测试发件人格式: "${sender}"`);
      
      // 1. 只按发件人搜索
      console.log('  1. 只按发件人搜索:');
      const senderOnlyResults = await emailService.searchEmails({
        email: email,
        auth_code: authCode,
        protocol: 'imap',
        sender: sender
      });
      console.log(`     结果: ${senderOnlyResults.length} 封邮件`);
      
      // 2. 发件人 + 日期搜索
      console.log('  2. 发件人 + 日期搜索 (2023-11-27):');
      const comboResults = await emailService.searchEmails({
        email: email,
        auth_code: authCode,
        protocol: 'imap',
        sender: sender,
        startDate: '2023-11-27',
        endDate: '2023-11-27'
      });
      console.log(`     结果: ${comboResults.length} 封邮件`);
      
      if (comboResults.length > 0) {
        console.log('     ✅ 找到匹配邮件！');
        comboResults.forEach((mail, index) => {
          console.log(`       ${index + 1}. ${mail.date} - ${mail.from}`);
        });
      }
    }
    
    // 测试直接使用IMAP搜索不同格式
    console.log('\n=== 直接IMAP测试 ===');
    await testDirectIMAP(email, authCode);
    
  } catch (error) {
    console.error('测试失败:', error.message);
  }
}

async function testDirectIMAP(email, authCode) {
  const Imap = require('imap');
  
  const imap = new Imap({
    host: 'imap.qq.com',
    port: 993,
    tls: true,
    user: email,
    password: authCode,
    connTimeout: 30000,
    authTimeout: 30000
  });
  
  return new Promise((resolve, reject) => {
    imap.once('ready', () => {
      console.log('IMAP连接就绪，测试发件人格式...');
      
      imap.openBox('INBOX', true, (err, box) => {
        if (err) {
          console.error('打开收件箱失败:', err);
          imap.end();
          reject(err);
          return;
        }
        
        // 测试不同的发件人搜索格式
        const testCases = [
          {
            name: '完整格式',
            criteria: ['FROM', 'hxuan25 <<EMAIL>>']
          },
          {
            name: '邮箱地址',
            criteria: ['FROM', '<EMAIL>']
          },
          {
            name: '用户名',
            criteria: ['FROM', 'hxuan25']
          },
          {
            name: '域名',
            criteria: ['FROM', 'concerthall.com.cn']
          },
          {
            name: '组合：邮箱+日期',
            criteria: [
              ['FROM', '<EMAIL>'],
              ['SINCE', new Date('2023-11-27')],
              ['BEFORE', new Date('2023-11-28')]
            ]
          },
          {
            name: '组合：完整格式+日期',
            criteria: [
              ['FROM', 'hxuan25 <<EMAIL>>'],
              ['SINCE', new Date('2023-11-27')],
              ['BEFORE', new Date('2023-11-28')]
            ]
          }
        ];
        
        let testIndex = 0;
        
        function testNext() {
          if (testIndex >= testCases.length) {
            imap.end();
            resolve();
            return;
          }
          
          const testCase = testCases[testIndex];
          console.log(`\n测试: ${testCase.name}`);
          console.log(`搜索条件: ${JSON.stringify(testCase.criteria)}`);
          
          imap.search(testCase.criteria, (err, results) => {
            if (err) {
              console.log(`  错误: ${err.message}`);
            } else {
              console.log(`  结果: ${results.length} 封邮件`);
              if (results.length > 0) {
                console.log('  ✅ 成功找到邮件！');
              }
            }
            
            testIndex++;
            setTimeout(testNext, 1000);
          });
        }
        
        testNext();
      });
    });
    
    imap.once('error', (err) => {
      console.error('IMAP错误:', err);
      reject(err);
    });
    
    imap.connect();
  });
}

// 运行测试
if (require.main === module) {
  testSenderFormat()
    .then(() => {
      console.log('\n发件人格式测试完成');
      process.exit(0);
    })
    .catch(error => {
      console.error('测试过程出错:', error);
      process.exit(1);
    });
}

module.exports = { testSenderFormat };
