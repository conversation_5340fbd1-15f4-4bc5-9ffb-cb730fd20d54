/**
 * 测试单个附件下载功能
 */

const attachmentService = require('./src/attachment-service');
const path = require('path');

async function testSingleAttachment() {
  const email = '<EMAIL>';
  const authCode = 'vzbropcxoztvbhea';
  
  console.log('=== 测试单个附件下载 ===');
  
  try {
    // 首先筛选有附件的邮件
    console.log('1. 筛选有附件的邮件...');
    
    const emailsWithAttachments = await attachmentService.filterEmailsWithAttachments(email, authCode, {
      maxEmails: 50 // 检查最近50封邮件
    });
    
    if (emailsWithAttachments.length === 0) {
      console.log('没有找到有附件的邮件');
      return;
    }
    
    // 选择第一封邮件进行测试
    const testEmail = emailsWithAttachments[0];
    console.log(`\n2. 选择测试邮件:`);
    console.log(`   UID: ${testEmail.uid}`);
    console.log(`   主题: ${testEmail.subject}`);
    console.log(`   附件数量: ${testEmail.attachments.length}`);
    
    // 显示附件信息
    testEmail.attachments.forEach((attachment, index) => {
      console.log(`   附件 ${index + 1}:`);
      console.log(`     原始文件名: ${attachment.filename}`);
      console.log(`     解码文件名: ${attachmentService.decodeMimeFilename ? attachmentService.decodeMimeFilename(attachment.filename) : '解码函数不可用'}`);
      console.log(`     类型: ${attachment.contentType}`);
      console.log(`     大小: ${attachment.size} 字节`);
      console.log(`     编码: ${attachment.encoding}`);
    });
    
    // 下载附件
    const downloadDir = path.join(__dirname, 'downloads', 'test_single', `email_${testEmail.uid}`);
    console.log(`\n3. 下载附件到: ${downloadDir}`);
    
    const downloadedFiles = await attachmentService.downloadAttachments(
      email,
      authCode,
      testEmail.uid,
      testEmail.attachments,
      downloadDir
    );
    
    console.log(`\n4. 下载结果:`);
    downloadedFiles.forEach((file, index) => {
      console.log(`   文件 ${index + 1}:`);
      console.log(`     文件名: ${file.filename}`);
      console.log(`     路径: ${file.path}`);
      console.log(`     大小: ${file.size} 字节`);
      console.log(`     原始名称: ${file.originalName}`);
    });
    
    console.log('\n✓ 单个附件下载测试完成');
    
  } catch (error) {
    console.error('测试失败:', error.message);
    console.error('错误详情:', error);
  }
}

// 运行测试
if (require.main === module) {
  testSingleAttachment()
    .then(() => {
      console.log('\n=== 测试完成 ===');
      process.exit(0);
    })
    .catch(error => {
      console.error('测试过程出错:', error);
      process.exit(1);
    });
}

module.exports = { testSingleAttachment };
