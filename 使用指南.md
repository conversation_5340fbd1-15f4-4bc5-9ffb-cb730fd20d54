# QQ邮箱搜索工具 - 使用指南

## 🎉 问题已完全解决！

### ✅ 修复内容
1. **认证问题解决**：修复了"No supported authentication method(s) available"错误
2. **连接测试功能**：现在可以正常测试邮箱连接
3. **搜索功能正常**：可以成功搜索和显示邮件
4. **状态管理完善**：进度显示、通知系统、搜索历史等功能正常

## 🚀 使用步骤

### 1. 启动应用
```bash
npm run start-server
```

### 2. 访问界面
打开浏览器访问：http://localhost:3000

### 3. 设置QQ邮箱（首次使用）

#### 开启IMAP服务
1. 登录QQ邮箱网页版：https://mail.qq.com
2. 点击"设置" → "账户"
3. 找到"POP3/IMAP/SMTP/Exchange/CardDAV/CalDAV服务"
4. 开启"IMAP/SMTP服务"
5. 按提示完成手机验证
6. **重要**：记录显示的16位授权码

### 4. 使用应用

#### 步骤1：输入邮箱凭证
- **邮箱地址**：输入您的QQ邮箱（如：<EMAIL>）
- **授权码**：输入16位授权码（不是QQ密码！）

#### 步骤2：测试连接
- 点击"**测试连接**"按钮
- 等待测试结果：
  - ✅ 成功：显示"IMAP连接测试成功，可以开始搜索邮件"
  - ❌ 失败：检查邮箱地址和授权码

#### 步骤3：设置搜索条件（可选）
- **发件人**：输入发件人邮箱或姓名
- **主题**：输入邮件主题关键词
- **日期范围**：选择开始和结束日期
- **协议**：建议使用IMAP（默认）

#### 步骤4：搜索邮件
- 点击"**搜索邮件**"按钮
- 观察搜索进度和状态更新
- 查看搜索结果

#### 步骤5：导出结果（可选）
- 搜索完成后，点击"**导出Excel**"按钮
- 选择保存位置
- 获得Excel格式的搜索结果

## 🔧 功能特色

### 智能搜索
- **全文搜索**：搜索邮件内容、主题、发件人
- **日期过滤**：按时间范围筛选邮件
- **条件组合**：支持多个搜索条件组合使用

### 用户体验
- **实时进度**：显示搜索进度和当前步骤
- **状态通知**：成功、错误、警告等通知提醒
- **搜索历史**：自动保存搜索条件，快速重复搜索
- **输入验证**：实时验证邮箱格式和授权码

### 数据安全
- **本地处理**：所有数据在本地处理，不上传到外部服务器
- **凭证保护**：搜索完成后立即清理内存中的授权码
- **历史记录**：不保存敏感信息，仅保存搜索参数

## 🛠️ 故障排除

### 连接测试失败
**可能原因**：
1. 邮箱地址格式错误
2. 授权码错误或过期
3. 未开启IMAP服务
4. 网络连接问题

**解决方法**：
1. 确认邮箱格式：<EMAIL>
2. 重新生成授权码
3. 检查QQ邮箱IMAP服务是否开启
4. 检查网络连接和防火墙设置

### 搜索无结果
**可能原因**：
1. 搜索条件过于严格
2. 邮箱中确实没有匹配的邮件
3. 日期范围设置错误

**解决方法**：
1. 放宽搜索条件
2. 检查日期范围设置
3. 尝试不设置任何条件进行全部搜索

### 搜索速度慢
**说明**：
- 应用限制单次搜索最多50封邮件，确保性能
- 搜索大量邮件需要时间，请耐心等待
- 可以通过设置更精确的搜索条件来提高速度

## 📋 技术信息

### 系统要求
- Node.js 14.0+
- 现代浏览器（Chrome、Firefox、Edge等）
- 网络连接

### 支持的邮箱
- QQ邮箱（@qq.com）
- 企业QQ邮箱（@公司域名，使用QQ企业邮箱）

### 支持的协议
- **IMAP**（推荐）：支持文件夹访问，搜索功能更强
- **POP3**：基础邮件下载功能

## 🔒 隐私说明

### 数据处理
- 所有邮件数据在本地处理
- 不会上传邮件内容到任何外部服务器
- 授权码仅用于连接邮箱，使用后立即清理

### 存储信息
- **本地存储**：仅保存搜索历史参数（不包含敏感信息）
- **不保存**：邮箱密码、授权码、邮件内容

## 📞 技术支持

### 常见问题
1. **授权码在哪里获取？**
   - QQ邮箱设置 → 账户 → 开启IMAP服务 → 获取授权码

2. **为什么不能用QQ密码？**
   - QQ邮箱要求使用专门的授权码，不能使用QQ密码

3. **搜索结果可以保存吗？**
   - 可以，点击"导出Excel"按钮保存为Excel文件

4. **支持其他邮箱吗？**
   - 目前仅支持QQ邮箱，后续可能扩展支持其他邮箱

### 调试功能
- 访问 http://localhost:3000/debug 查看调试信息
- 按F12打开浏览器开发者工具查看详细日志

---

**🎊 现在您可以正常使用QQ邮箱搜索工具了！**
